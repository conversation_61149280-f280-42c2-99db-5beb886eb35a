{% extends 'index.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="oh-wrapper">
    <div class="oh-main-wrapper">
        <div class="oh-main">
            <div class="oh-main__header">
                <div class="oh-main__header-title">
                    <h1 class="oh-main__header-title-text">
                        <i class="oh-icon oh-icon--history"></i>
                        {% trans "Bulk Update History" %}
                    </h1>
                    <p class="oh-main__header-subtitle">
                        {% trans "View all your bulk update operations" %}
                    </p>
                </div>
                <div class="oh-main__header-actions">
                    <a href="{% url 'bulk-update-upload' %}" class="oh-btn oh-btn--primary">
                        <i class="oh-icon oh-icon--plus"></i>
                        {% trans "New Bulk Update" %}
                    </a>
                </div>
            </div>

            <div class="oh-main__body">
                {% if operations %}
                <div class="oh-card">
                    <div class="oh-card__header">
                        <h3 class="oh-card__title">
                            <i class="oh-icon oh-icon--list"></i>
                            {% trans "Operations History" %}
                        </h3>
                    </div>
                    <div class="oh-card__body">
                        <div class="oh-table-wrapper">
                            <table class="oh-table oh-table--striped">
                                <thead>
                                    <tr>
                                        <th>{% trans "Date" %}</th>
                                        <th>{% trans "Operation Type" %}</th>
                                        <th>{% trans "File" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Records" %}</th>
                                        <th>{% trans "Success Rate" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for operation in operations %}
                                    <tr>
                                        <td>
                                            <div class="oh-date-info">
                                                <strong>{{ operation.created_at|date:"M d, Y" }}</strong><br>
                                                <small>{{ operation.created_at|time:"H:i" }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="oh-operation-type">
                                                {{ operation.get_operation_type_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="oh-file-info">
                                                <strong>{{ operation.original_filename }}</strong><br>
                                                <small>{{ operation.file_size|filesizeformat }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            {% if operation.status == 'completed' %}
                                                <span class="oh-badge oh-badge--success">{% trans "Completed" %}</span>
                                            {% elif operation.status == 'failed' %}
                                                <span class="oh-badge oh-badge--danger">{% trans "Failed" %}</span>
                                            {% elif operation.status == 'processing' %}
                                                <span class="oh-badge oh-badge--warning">{% trans "Processing" %}</span>
                                            {% elif operation.status == 'pending' %}
                                                <span class="oh-badge oh-badge--info">{% trans "Pending" %}</span>
                                            {% else %}
                                                <span class="oh-badge oh-badge--secondary">{{ operation.get_status_display }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="oh-records-info">
                                                <strong>{{ operation.total_records }}</strong> {% trans "total" %}<br>
                                                {% if operation.status == 'completed' %}
                                                    <small>
                                                        <span class="oh-text-success">{{ operation.successful_updates }} {% trans "success" %}</span>
                                                        {% if operation.failed_updates > 0 %}
                                                            / <span class="oh-text-danger">{{ operation.failed_updates }} {% trans "failed" %}</span>
                                                        {% endif %}
                                                    </small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            {% if operation.status == 'completed' and operation.total_records > 0 %}
                                                {% widthratio operation.successful_updates operation.total_records 100 as success_rate %}
                                                <div class="oh-progress-wrapper">
                                                    <div class="oh-progress">
                                                        <div class="oh-progress__bar 
                                                            {% if success_rate >= 90 %}oh-progress__bar--success
                                                            {% elif success_rate >= 70 %}oh-progress__bar--warning
                                                            {% else %}oh-progress__bar--danger{% endif %}"
                                                             style="width: {{ success_rate }}%"></div>
                                                    </div>
                                                    <small>{{ success_rate }}%</small>
                                                </div>
                                            {% else %}
                                                <span class="oh-text-muted">{% trans "N/A" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="oh-btn-group oh-btn-group--sm">
                                                {% if operation.status == 'completed' %}
                                                    <a href="{% url 'bulk-update-results' operation.id %}" 
                                                       class="oh-btn oh-btn--sm oh-btn--outline">
                                                        <i class="oh-icon oh-icon--eye"></i>
                                                        {% trans "View Results" %}
                                                    </a>
                                                {% elif operation.status == 'pending' and operation.preview %}
                                                    <a href="{% url 'bulk-update-preview' operation.id %}" 
                                                       class="oh-btn oh-btn--sm oh-btn--outline">
                                                        <i class="oh-icon oh-icon--preview"></i>
                                                        {% trans "Continue" %}
                                                    </a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="oh-card">
                    <div class="oh-card__body">
                        <div class="oh-empty-state">
                            <i class="oh-icon oh-icon--empty"></i>
                            <h3>{% trans "No Bulk Updates Yet" %}</h3>
                            <p>{% trans "You haven't performed any bulk updates yet. Start by uploading a file to update employee data." %}</p>
                            <a href="{% url 'bulk-update-upload' %}" class="oh-btn oh-btn--primary">
                                <i class="oh-icon oh-icon--plus"></i>
                                {% trans "Start Bulk Update" %}
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.oh-date-info, .oh-file-info, .oh-records-info {
    line-height: 1.4;
}

.oh-operation-type {
    font-weight: 500;
    color: #495057;
}

.oh-progress-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.oh-progress {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.oh-progress__bar {
    height: 100%;
    transition: width 0.3s ease;
}

.oh-progress__bar--success { background: #28a745; }
.oh-progress__bar--warning { background: #ffc107; }
.oh-progress__bar--danger { background: #dc3545; }

.oh-empty-state {
    text-align: center;
    padding: 3rem;
}

.oh-empty-state i {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.oh-btn-group--sm .oh-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.oh-text-success { color: #28a745; }
.oh-text-danger { color: #dc3545; }
.oh-text-muted { color: #6c757d; }
</style>
{% endblock %}
