{% extends 'index.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="oh-wrapper">
    <div class="oh-main-wrapper">
        <div class="oh-main">
            <div class="oh-main__header">
                <div class="oh-main__header-title">
                    <h1 class="oh-main__header-title-text">
                        <i class="oh-icon oh-icon--upload"></i>
                        {% trans "Bulk Update Employee Data" %}
                    </h1>
                    <p class="oh-main__header-subtitle">
                        {% trans "Upload Excel or CSV files to update employee information in bulk" %}
                    </p>
                </div>
                <div class="oh-main__header-actions">
                    <a href="{% url 'bulk-update-history' %}" class="oh-btn oh-btn--secondary">
                        <i class="oh-icon oh-icon--history"></i>
                        {% trans "View History" %}
                    </a>
                </div>
            </div>

            <div class="oh-main__body">
                <!-- Instructions Card -->
                <div class="oh-card oh-card--info mb-4">
                    <div class="oh-card__header">
                        <h3 class="oh-card__title">
                            <i class="oh-icon oh-icon--info"></i>
                            {% trans "Instructions" %}
                        </h3>
                    </div>
                    <div class="oh-card__body">
                        <ol class="oh-list oh-list--ordered">
                            <li>{% trans "Choose the type of data you want to update" %}</li>
                            <li>{% trans "Select how employees will be identified in your file (Email, Badge ID, or Employee ID)" %}</li>
                            <li>{% trans "Download the appropriate template or prepare your file with the correct columns" %}</li>
                            <li>{% trans "Upload your Excel (.xlsx, .xls) or CSV (.csv) file" %}</li>
                            <li>{% trans "Review the preview and confirm your updates" %}</li>
                        </ol>
                        <div class="oh-alert oh-alert--warning mt-3">
                            <i class="oh-icon oh-icon--warning"></i>
                            {% trans "Important: Bulk updates cannot be undone. Please review your data carefully before confirming." %}
                        </div>
                    </div>
                </div>

                <!-- Upload Form -->
                <div class="oh-card">
                    <div class="oh-card__header">
                        <h3 class="oh-card__title">
                            <i class="oh-icon oh-icon--upload"></i>
                            {% trans "Upload File" %}
                        </h3>
                    </div>
                    <div class="oh-card__body">
                        <form method="post" enctype="multipart/form-data" class="oh-form">
                            {% csrf_token %}
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="oh-form__group">
                                        <label for="{{ form.operation_type.id_for_label }}" class="oh-form__label">
                                            {{ form.operation_type.label }}
                                        </label>
                                        {{ form.operation_type }}
                                        {% if form.operation_type.help_text %}
                                            <small class="oh-form__help-text">{{ form.operation_type.help_text }}</small>
                                        {% endif %}
                                        {% if form.operation_type.errors %}
                                            <div class="oh-form__error">{{ form.operation_type.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="oh-form__group">
                                        <label for="{{ form.employee_identifier.id_for_label }}" class="oh-form__label">
                                            {{ form.employee_identifier.label }}
                                        </label>
                                        {{ form.employee_identifier }}
                                        {% if form.employee_identifier.help_text %}
                                            <small class="oh-form__help-text">{{ form.employee_identifier.help_text }}</small>
                                        {% endif %}
                                        {% if form.employee_identifier.errors %}
                                            <div class="oh-form__error">{{ form.employee_identifier.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="oh-form__group">
                                <label for="{{ form.file.id_for_label }}" class="oh-form__label">
                                    {{ form.file.label }}
                                </label>
                                {{ form.file }}
                                {% if form.file.help_text %}
                                    <small class="oh-form__help-text">{{ form.file.help_text }}</small>
                                {% endif %}
                                {% if form.file.errors %}
                                    <div class="oh-form__error">{{ form.file.errors }}</div>
                                {% endif %}
                            </div>

                            <div class="oh-form__group">
                                <label class="oh-form__label">
                                    {{ form.update_fields.label }}
                                </label>
                                <div class="oh-checkbox-group">
                                    {{ form.update_fields }}
                                </div>
                                {% if form.update_fields.help_text %}
                                    <small class="oh-form__help-text">{{ form.update_fields.help_text }}</small>
                                {% endif %}
                                {% if form.update_fields.errors %}
                                    <div class="oh-form__error">{{ form.update_fields.errors }}</div>
                                {% endif %}
                            </div>

                            <div class="oh-form__actions">
                                <button type="submit" class="oh-btn oh-btn--primary">
                                    <i class="oh-icon oh-icon--upload"></i>
                                    {% trans "Process File" %}
                                </button>
                                <button type="button" class="oh-btn oh-btn--secondary" onclick="downloadTemplate()">
                                    <i class="oh-icon oh-icon--download"></i>
                                    {% trans "Download Template" %}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Template Download Section -->
                <div class="oh-card mt-4">
                    <div class="oh-card__header">
                        <h3 class="oh-card__title">
                            <i class="oh-icon oh-icon--template"></i>
                            {% trans "Download Templates" %}
                        </h3>
                    </div>
                    <div class="oh-card__body">
                        <p>{% trans "Download pre-formatted templates for different types of updates:" %}</p>
                        <div class="oh-btn-group">
                            <a href="{% url 'bulk-update-template-download' %}?type=employee_update&identifier=email" 
                               class="oh-btn oh-btn--outline">
                                {% trans "Employee Info Template" %}
                            </a>
                            <a href="{% url 'bulk-update-template-download' %}?type=work_info_update&identifier=email" 
                               class="oh-btn oh-btn--outline">
                                {% trans "Work Info Template" %}
                            </a>
                            <a href="{% url 'bulk-update-template-download' %}?type=compliance_update&identifier=email" 
                               class="oh-btn oh-btn--outline">
                                {% trans "Nigerian Compliance Template" %}
                            </a>
                            <a href="{% url 'bulk-update-template-download' %}?type=mixed_update&identifier=email" 
                               class="oh-btn oh-btn--outline">
                                {% trans "Complete Template" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function downloadTemplate() {
    const operationType = document.getElementById('id_operation_type').value;
    const identifierType = document.getElementById('id_employee_identifier').value;
    
    if (!operationType) {
        alert('{% trans "Please select an operation type first" %}');
        return;
    }
    
    const url = `{% url 'bulk-update-template-download' %}?type=${operationType}&identifier=${identifierType}`;
    window.location.href = url;
}

// Show/hide field selection based on operation type
document.getElementById('id_operation_type').addEventListener('change', function() {
    const fieldSelection = document.querySelector('.oh-checkbox-group').closest('.oh-form__group');
    if (this.value === 'mixed_update') {
        fieldSelection.style.display = 'block';
    } else {
        fieldSelection.style.display = 'none';
    }
});

// Initialize field selection visibility
document.addEventListener('DOMContentLoaded', function() {
    const operationType = document.getElementById('id_operation_type').value;
    const fieldSelection = document.querySelector('.oh-checkbox-group').closest('.oh-form__group');
    if (operationType !== 'mixed_update') {
        fieldSelection.style.display = 'none';
    }
});
</script>
{% endblock %}
