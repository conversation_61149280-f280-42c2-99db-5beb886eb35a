<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alert Styling Test</title>
    <link rel="stylesheet" href="static/build/css/style.min.css">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        h2 {
            color: #333;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Employee Alert Styling Test</h1>
        
        <div class="test-section">
            <h2>Original Alert (Before Fix)</h2>
            <div class="oh-alert oh-alert--info mb-2" style="padding: 1rem;">
                <div class="d-flex justify-content-between align-items-center">
                    <span>
                        <i class="oh-icon oh-icon--info"></i>
                        Showing employees from selected company only.
                    </span>
                    <a href="#" class="oh-btn oh-btn--secondary oh-btn--sm">
                        Show All Companies
                    </a>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Fixed Alert (After Fix)</h2>
            <div class="oh-alert oh-alert--info mb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <span>
                        <i class="oh-icon oh-icon--info"></i>
                        Showing employees from selected company only.
                    </span>
                    <a href="#" class="oh-btn oh-btn--secondary oh-btn--sm">
                        Show All Companies
                    </a>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Table-Integrated Alert (Final Fix)</h2>
            <div id="employee-table" style="border: 1px solid #ddd; border-radius: 4px; overflow: hidden;">
                <div class="oh-sticky-table">
                    <div class="oh-sticky-table__table">
                        <div class="oh-sticky-table__thead">
                            <div class="oh-sticky-table__tr">
                                <div class="oh-sticky-table__th">Employee</div>
                                <div class="oh-sticky-table__th">Email</div>
                                <div class="oh-sticky-table__th">Phone</div>
                                <div class="oh-sticky-table__th">Actions</div>
                            </div>
                        </div>

                        <!-- Alert integrated within table -->
                        <div class="oh-alert oh-alert--info mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>
                                    <i class="oh-icon oh-icon--info"></i>
                                    Showing employees from selected company only.
                                </span>
                                <a href="#" class="oh-btn oh-btn--secondary oh-btn--sm">
                                    Show All Companies
                                </a>
                            </div>
                        </div>

                        <div class="oh-sticky-table__tbody">
                            <div class="oh-sticky-table__tr">
                                <div class="oh-sticky-table__td">John Doe</div>
                                <div class="oh-sticky-table__td"><EMAIL></div>
                                <div class="oh-sticky-table__td">123-456-7890</div>
                                <div class="oh-sticky-table__td">Edit | Delete</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Comparison & Analysis</h2>
            <p><strong>Issues Identified from Screenshot:</strong></p>
            <ul>
                <li>Large empty space between table header and data rows</li>
                <li>Alert element not properly integrated with table layout</li>
                <li>Excessive height causing visual disruption</li>
                <li>Poor alignment with table structure</li>
            </ul>

            <p><strong>Changes Made:</strong></p>
            <ul>
                <li><strong>Height Reduction:</strong> Reduced padding from 1rem to 0.5rem 0.75rem (~50% reduction)</li>
                <li><strong>Table Integration:</strong> Added specific styling for alerts within #employee-table</li>
                <li><strong>Layout Fix:</strong> Used table-row and table-cell display for proper integration</li>
                <li><strong>Border Consistency:</strong> Matched table border styling and colors</li>
                <li><strong>Visual Cohesion:</strong> Removed box shadows and rounded corners to match table</li>
                <li><strong>Color Improvements:</strong> Enhanced text contrast and button styling</li>
                <li><strong>Responsive Design:</strong> Maintained responsive behavior across screen sizes</li>
                <li><strong>Micro-interactions:</strong> Added smooth transitions and hover effects</li>
            </ul>

            <p><strong>Result:</strong></p>
            <ul>
                <li>Eliminated the large gap shown in the screenshot</li>
                <li>Alert now seamlessly integrates with table structure</li>
                <li>Improved visual hierarchy and readability</li>
                <li>Maintained functionality while fixing layout issues</li>
            </ul>
        </div>
    </div>

    <!-- Add some basic Bootstrap classes for flexbox -->
    <style>
        .d-flex {
            display: flex !important;
        }
        .justify-content-between {
            justify-content: space-between !important;
        }
        .align-items-center {
            align-items: center !important;
        }
        .mb-2 {
            margin-bottom: 0.5rem !important;
        }
        .oh-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }
        .oh-icon--info::before {
            content: "ℹ";
            color: #0444ce;
            font-weight: bold;
        }

        /* Basic table styling for demo */
        .oh-sticky-table {
            border: 1px solid #e9ecef;
            background: white;
        }
        .oh-sticky-table__table {
            width: 100%;
            display: table;
        }
        .oh-sticky-table__thead {
            display: table-header-group;
            background: #f8f9fa;
        }
        .oh-sticky-table__tbody {
            display: table-row-group;
        }
        .oh-sticky-table__tr {
            display: table-row;
        }
        .oh-sticky-table__th,
        .oh-sticky-table__td {
            display: table-cell;
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
            text-align: left;
        }
        .oh-sticky-table__th {
            font-weight: 600;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
        }
    </style>
</body>
</html>
