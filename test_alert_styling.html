<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alert Styling Test</title>
    <link rel="stylesheet" href="static/build/css/style.min.css">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        h2 {
            color: #333;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Employee Alert Styling Test</h1>
        
        <div class="test-section">
            <h2>Original Alert (Before Fix)</h2>
            <div class="oh-alert oh-alert--info mb-2" style="padding: 1rem;">
                <div class="d-flex justify-content-between align-items-center">
                    <span>
                        <i class="oh-icon oh-icon--info"></i>
                        Showing employees from selected company only.
                    </span>
                    <a href="#" class="oh-btn oh-btn--secondary oh-btn--sm">
                        Show All Companies
                    </a>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Fixed Alert (After Fix)</h2>
            <div class="oh-alert oh-alert--info mb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <span>
                        <i class="oh-icon oh-icon--info"></i>
                        Showing employees from selected company only.
                    </span>
                    <a href="#" class="oh-btn oh-btn--secondary oh-btn--sm">
                        Show All Companies
                    </a>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Comparison</h2>
            <p><strong>Changes Made:</strong></p>
            <ul>
                <li>Reduced padding from 1rem to 0.5rem 0.75rem (height reduction)</li>
                <li>Improved text color contrast for better readability</li>
                <li>Enhanced button styling with better colors and hover effects</li>
                <li>Added smooth transitions and micro-interactions</li>
                <li>Removed default alert icon (::before) to avoid duplication</li>
            </ul>
        </div>
    </div>

    <!-- Add some basic Bootstrap classes for flexbox -->
    <style>
        .d-flex {
            display: flex !important;
        }
        .justify-content-between {
            justify-content: space-between !important;
        }
        .align-items-center {
            align-items: center !important;
        }
        .mb-2 {
            margin-bottom: 0.5rem !important;
        }
        .oh-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }
        .oh-icon--info::before {
            content: "ℹ";
            color: #0444ce;
            font-weight: bold;
        }
    </style>
</body>
</html>
