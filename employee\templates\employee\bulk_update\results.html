{% extends 'index.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="oh-wrapper">
    <div class="oh-main-wrapper">
        <div class="oh-main">
            <div class="oh-main__header">
                <div class="oh-main__header-title">
                    <h1 class="oh-main__header-title-text">
                        <i class="oh-icon oh-icon--results"></i>
                        {% trans "Bulk Update Results" %}
                    </h1>
                    <p class="oh-main__header-subtitle">
                        {% trans "Summary of bulk update operation" %}
                    </p>
                </div>
                <div class="oh-main__header-actions">
                    <a href="{% url 'bulk-update-upload' %}" class="oh-btn oh-btn--primary">
                        <i class="oh-icon oh-icon--plus"></i>
                        {% trans "New Bulk Update" %}
                    </a>
                    <a href="{% url 'bulk-update-history' %}" class="oh-btn oh-btn--secondary">
                        <i class="oh-icon oh-icon--history"></i>
                        {% trans "View History" %}
                    </a>
                </div>
            </div>

            <div class="oh-main__body">
                <!-- Operation Summary -->
                <div class="oh-card oh-card--summary mb-4">
                    <div class="oh-card__header">
                        <h3 class="oh-card__title">
                            <i class="oh-icon oh-icon--summary"></i>
                            {% trans "Operation Summary" %}
                        </h3>
                        <div class="oh-card__status">
                            {% if operation.status == 'completed' %}
                                <span class="oh-badge oh-badge--success">{% trans "Completed" %}</span>
                            {% elif operation.status == 'failed' %}
                                <span class="oh-badge oh-badge--danger">{% trans "Failed" %}</span>
                            {% else %}
                                <span class="oh-badge oh-badge--warning">{{ operation.get_status_display }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="oh-card__body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="oh-stat">
                                    <div class="oh-stat__value">{{ operation.total_records }}</div>
                                    <div class="oh-stat__label">{% trans "Total Records" %}</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="oh-stat oh-stat--success">
                                    <div class="oh-stat__value">{{ operation.successful_updates }}</div>
                                    <div class="oh-stat__label">{% trans "Successful Updates" %}</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="oh-stat oh-stat--danger">
                                    <div class="oh-stat__value">{{ operation.failed_updates }}</div>
                                    <div class="oh-stat__label">{% trans "Failed Updates" %}</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="oh-stat">
                                    <div class="oh-stat__value">{{ operation.progress_percentage|floatformat:1 }}%</div>
                                    <div class="oh-stat__label">{% trans "Progress" %}</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="oh-operation-details mt-4">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>{% trans "Operation Type:" %}</strong> {{ operation.get_operation_type_display }}</p>
                                    <p><strong>{% trans "File:" %}</strong> {{ operation.original_filename }}</p>
                                    <p><strong>{% trans "File Size:" %}</strong> {{ operation.file_size|filesizeformat }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>{% trans "Started:" %}</strong> {{ operation.started_at|date:"Y-m-d H:i:s" }}</p>
                                    <p><strong>{% trans "Completed:" %}</strong> {{ operation.completed_at|date:"Y-m-d H:i:s" }}</p>
                                    <p><strong>{% trans "Duration:" %}</strong> 
                                        {% if operation.started_at and operation.completed_at %}
                                            {{ operation.completed_at|timesince:operation.started_at }}
                                        {% else %}
                                            {% trans "N/A" %}
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>

                        {% if operation.error_message %}
                        <div class="oh-alert oh-alert--danger mt-3">
                            <strong>{% trans "Error:" %}</strong> {{ operation.error_message }}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Success Records -->
                {% if success_records %}
                <div class="oh-card mb-4">
                    <div class="oh-card__header">
                        <h3 class="oh-card__title">
                            <i class="oh-icon oh-icon--check"></i>
                            {% trans "Successful Updates" %}
                            <span class="oh-badge oh-badge--success">{{ success_records.count }}</span>
                        </h3>
                    </div>
                    <div class="oh-card__body">
                        <div class="oh-table-wrapper">
                            <table class="oh-table oh-table--striped">
                                <thead>
                                    <tr>
                                        <th>{% trans "Row" %}</th>
                                        <th>{% trans "Employee" %}</th>
                                        <th>{% trans "Identifier" %}</th>
                                        <th>{% trans "Updates Applied" %}</th>
                                        <th>{% trans "Processed At" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in success_records %}
                                    <tr>
                                        <td>{{ record.row_number }}</td>
                                        <td>
                                            {% if record.employee %}
                                                <a href="{% url 'employee-view-individual' record.employee.id %}" 
                                                   class="oh-link">
                                                    {{ record.employee.employee_first_name }} {{ record.employee.employee_last_name }}
                                                </a>
                                            {% else %}
                                                {% trans "Unknown" %}
                                            {% endif %}
                                        </td>
                                        <td>{{ record.employee_identifier }}</td>
                                        <td>
                                            {% if record.processed_data %}
                                                <details>
                                                    <summary>{% trans "View Updates" %}</summary>
                                                    <div class="oh-updates-detail">
                                                        {% for model, fields in record.processed_data.items %}
                                                            {% if fields %}
                                                                <h6>{{ model|title }}</h6>
                                                                <ul>
                                                                    {% for field, value in fields.items %}
                                                                        <li><strong>{{ field }}:</strong> {{ value }}</li>
                                                                    {% endfor %}
                                                                </ul>
                                                            {% endif %}
                                                        {% endfor %}
                                                    </div>
                                                </details>
                                            {% else %}
                                                {% trans "No data" %}
                                            {% endif %}
                                        </td>
                                        <td>{{ record.processed_at|date:"Y-m-d H:i:s" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Failed Records -->
                {% if failed_records %}
                <div class="oh-card oh-card--danger">
                    <div class="oh-card__header">
                        <h3 class="oh-card__title">
                            <i class="oh-icon oh-icon--error"></i>
                            {% trans "Failed Updates" %}
                            <span class="oh-badge oh-badge--danger">{{ failed_records.count }}</span>
                        </h3>
                    </div>
                    <div class="oh-card__body">
                        <div class="oh-table-wrapper">
                            <table class="oh-table oh-table--striped">
                                <thead>
                                    <tr>
                                        <th>{% trans "Row" %}</th>
                                        <th>{% trans "Identifier" %}</th>
                                        <th>{% trans "Error Message" %}</th>
                                        <th>{% trans "Original Data" %}</th>
                                        <th>{% trans "Processed At" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in failed_records %}
                                    <tr>
                                        <td>{{ record.row_number }}</td>
                                        <td>{{ record.employee_identifier }}</td>
                                        <td class="oh-text-danger">{{ record.error_message }}</td>
                                        <td>
                                            {% if record.original_data %}
                                                <details>
                                                    <summary>{% trans "View Data" %}</summary>
                                                    <pre class="oh-code-block">{{ record.original_data|pprint }}</pre>
                                                </details>
                                            {% else %}
                                                {% trans "No data" %}
                                            {% endif %}
                                        </td>
                                        <td>{{ record.processed_at|date:"Y-m-d H:i:s" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- No Records Message -->
                {% if not success_records and not failed_records %}
                <div class="oh-card">
                    <div class="oh-card__body">
                        <div class="oh-empty-state">
                            <i class="oh-icon oh-icon--empty"></i>
                            <h3>{% trans "No Records Processed" %}</h3>
                            <p>{% trans "No employee records were processed in this operation." %}</p>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.oh-stat {
    text-align: center;
    padding: 1rem;
    border-radius: 8px;
    background: #f8f9fa;
}

.oh-stat--success { background: #d4edda; }
.oh-stat--danger { background: #f8d7da; }

.oh-stat__value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.oh-stat__label {
    font-size: 0.875rem;
    color: #6c757d;
}

.oh-updates-detail {
    max-height: 200px;
    overflow-y: auto;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    margin-top: 0.5rem;
}

.oh-code-block {
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    max-height: 150px;
    overflow-y: auto;
}

.oh-empty-state {
    text-align: center;
    padding: 3rem;
}

.oh-empty-state i {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.oh-card__status {
    margin-left: auto;
}
</style>
{% endblock %}
