from django.core.management.base import BaseCommand
from django.db import transaction
from employee.models import Employee, EmployeeNigerianCompliance, EmployeeExtendedContract


class Command(BaseCommand):
    help = 'Create missing Nigerian Compliance and Extended Contract records for all employees'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating records',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output for each employee processed',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        verbose = options['verbose']
        
        self.stdout.write(
            self.style.SUCCESS('Starting Nigerian Compliance Records Creation Process')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No records will be created')
            )
        
        # Get all employees
        all_employees = Employee.objects.all()
        total_employees = all_employees.count()
        
        self.stdout.write(f'Found {total_employees} total employees')
        
        # Find employees without compliance records
        employees_without_compliance = Employee.objects.filter(
            nigerian_compliance__isnull=True
        )
        
        # Find employees without extended contract records
        employees_without_extended = Employee.objects.filter(
            extended_contract__isnull=True
        )
        
        compliance_count = employees_without_compliance.count()
        extended_count = employees_without_extended.count()
        
        self.stdout.write(f'Employees missing Nigerian Compliance records: {compliance_count}')
        self.stdout.write(f'Employees missing Extended Contract records: {extended_count}')
        
        if not dry_run:
            created_compliance = 0
            created_extended = 0
            
            with transaction.atomic():
                # Create missing Nigerian Compliance records
                for employee in employees_without_compliance:
                    try:
                        compliance_record, created = EmployeeNigerianCompliance.objects.get_or_create(
                            employee_id=employee
                        )
                        if created:
                            created_compliance += 1
                            if verbose:
                                self.stdout.write(
                                    f'Created compliance record for: {employee.employee_first_name} {employee.employee_last_name} (ID: {employee.id})'
                                )
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(
                                f'Error creating compliance record for employee {employee.id}: {e}'
                            )
                        )
                
                # Create missing Extended Contract records
                for employee in employees_without_extended:
                    try:
                        extended_record, created = EmployeeExtendedContract.objects.get_or_create(
                            employee_id=employee,
                            defaults={
                                'notice_period_days': 30,
                                'nhf_voluntary_status': True,
                            }
                        )
                        if created:
                            created_extended += 1
                            if verbose:
                                self.stdout.write(
                                    f'Created extended contract record for: {employee.employee_first_name} {employee.employee_last_name} (ID: {employee.id})'
                                )
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(
                                f'Error creating extended contract record for employee {employee.id}: {e}'
                            )
                        )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created {created_compliance} Nigerian Compliance records'
                )
            )
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created {created_extended} Extended Contract records'
                )
            )
        else:
            # Dry run - just show what would be created
            self.stdout.write('\nEmployees that would get Nigerian Compliance records:')
            for employee in employees_without_compliance[:10]:  # Show first 10
                self.stdout.write(
                    f'  - {employee.employee_first_name} {employee.employee_last_name} (ID: {employee.id}, Email: {employee.email})'
                )
            if compliance_count > 10:
                self.stdout.write(f'  ... and {compliance_count - 10} more employees')
            
            self.stdout.write('\nEmployees that would get Extended Contract records:')
            for employee in employees_without_extended[:10]:  # Show first 10
                self.stdout.write(
                    f'  - {employee.employee_first_name} {employee.employee_last_name} (ID: {employee.id}, Email: {employee.email})'
                )
            if extended_count > 10:
                self.stdout.write(f'  ... and {extended_count - 10} more employees')
        
        # Final verification
        final_compliance_count = EmployeeNigerianCompliance.objects.count()
        final_extended_count = EmployeeExtendedContract.objects.count()
        
        self.stdout.write('\n' + '='*50)
        self.stdout.write('FINAL SUMMARY:')
        self.stdout.write(f'Total employees: {total_employees}')
        self.stdout.write(f'Nigerian Compliance records: {final_compliance_count}')
        self.stdout.write(f'Extended Contract records: {final_extended_count}')
        
        if final_compliance_count == total_employees and final_extended_count == total_employees:
            self.stdout.write(
                self.style.SUCCESS('✅ All employees now have complete compliance records!')
            )
        else:
            missing_compliance = total_employees - final_compliance_count
            missing_extended = total_employees - final_extended_count
            if missing_compliance > 0:
                self.stdout.write(
                    self.style.WARNING(f'⚠️  {missing_compliance} employees still missing compliance records')
                )
            if missing_extended > 0:
                self.stdout.write(
                    self.style.WARNING(f'⚠️  {missing_extended} employees still missing extended contract records')
                )
