@charset "UTF-8";
/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
/* Document
   ========================================================================== */
/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
  line-height: 1.15; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
}

/* Sections
   ========================================================================== */
/**
 * Remove the margin in all browsers.
 */
body {
  margin: 0;
}

/**
 * Render the `main` element consistently in IE.
 */
main {
  display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1, .h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
   ========================================================================== */
/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
  box-sizing: content-box; /* 1 */
  height: 0; /* 1 */
  overflow: visible; /* 2 */
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/* Text-level semantics
   ========================================================================== */
/**
 * Remove the gray background on active links in IE 10.
 */
a {
  background-color: transparent;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
  border-bottom: none; /* 1 */
  text-decoration: underline; /* 2 */
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted; /* 2 */
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
  font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/**
 * Add the correct font size in all browsers.
 */
small, .small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
   ========================================================================== */
/**
 * Remove the border on images inside links in IE 10.
 */
img {
  border-style: none;
}

/* Forms
   ========================================================================== */
/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-size: 100%; /* 1 */
  line-height: 1.15; /* 1 */
  margin: 0; /* 2 */
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input { /* 1 */
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select { /* 1 */
  text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */
button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type=button]:-moz-focusring,
[type=reset]:-moz-focusring,
[type=submit]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */
fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
legend {
  box-sizing: border-box; /* 1 */
  color: inherit; /* 2 */
  display: table; /* 1 */
  max-width: 100%; /* 1 */
  padding: 0; /* 3 */
  white-space: normal; /* 1 */
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
  vertical-align: baseline;
}

/**
 * Remove the default vertical scrollbar in IE 10+.
 */
textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */
[type=checkbox],
[type=radio] {
  box-sizing: border-box; /* 1 */
  padding: 0; /* 2 */
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type=search] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/* Interactive
   ========================================================================== */
/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
details {
  display: block;
}

/*
 * Add the correct display in all browsers.
 */
summary {
  display: list-item;
}

/* Misc
   ========================================================================== */
/**
 * Add the correct display in IE 10+.
 */
template {
  display: none;
}

/**
 * Add the correct display in IE 10.
 */
[hidden] {
  display: none;
}

/*
 * jQuery OrgChart Plugin
 * https://github.com/dabeng/OrgChart
 *
 * Copyright 2016, dabeng
 * https://github.com/dabeng
 *
 * Licensed under the MIT license:
 * http://www.opensource.org/licenses/MIT
 */
/* chart styles */
.orgchart {
  box-sizing: border-box;
  display: inline-block;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  background-image: linear-gradient(to top, rgba(200, 0, 0, 0.15) 5%, rgba(0, 0, 0, 0) 5%), linear-gradient(to right, rgba(200, 0, 0, 0.15) 5%, rgba(0, 0, 0, 0) 5%), linear-gradient(to bottom, rgba(200, 0, 0, 0.15) 5%, rgba(0, 0, 0, 0) 5%), linear-gradient(to left, rgba(200, 0, 0, 0.15) 5%, rgba(0, 0, 0, 0) 5%);
  background-size: 10px 10px; /* background square size */
  padding: 20px 20px 0 20px;
  /* border: 0.5px solid rgba(200, 0, 0, 0.15); */
}

.orgchart .hidden,
.orgchart ~ .hidden {
  display: none !important;
}

.orgchart.b2t {
  transform: rotate(180deg);
}

.orgchart.l2r {
  position: absolute;
  transform: rotate(-90deg) rotateY(180deg);
  transform-origin: left top;
  text-align: center;
}

.orgchart.r2l {
  position: absolute;
  transform: rotate(90deg);
  transform-origin: left top;
  text-align: center;
}

.orgchart ~ .mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.3);
}

.orgchart ~ .mask .spinner {
  position: absolute;
  top: calc(50% - 50px);
  left: calc(50% - 50px);
}

.orgchart > .spinner::before,
.orgchart ~ .mask .spinner::before {
  width: 100px;
  height: 100px;
  border-width: 10px;
  border-radius: 50px;
  border-top-color: rgba(68, 157, 68, 0.8);
  border-bottom-color: rgba(68, 157, 68, 0.8);
  border-left-color: rgba(68, 157, 68, 0.8);
}

.orgchart .nodes {
  display: flex;
  list-style: none;
  padding-left: 0;
  margin: 0;
}

.orgchart .hierarchy {
  position: relative;
}

/* styles of link lines */
.orgchart .hierarchy::before {
  content: "";
  position: absolute;
  top: -11px; /* -(background square size + half width of line) */
  left: 0;
  width: 100%;
  border-top: 2px solid rgba(217, 83, 79, 0.8);
  box-sizing: border-box;
}

.orgchart .hierarchy:first-child::before,
.orgchart .hierarchy.isSiblingsCollapsed.left-sibs::before {
  left: calc(50% - 1px);
  width: calc(50% + 1px);
}

.orgchart .hierarchy:last-child::before,
.orgchart .hierarchy.isSiblingsCollapsed.right-sibs::before {
  width: calc(50% + 1px);
}

.orgchart .hierarchy:not(.hidden):only-child::before {
  width: 2px;
}

.orgchart > .nodes > .hierarchy::before,
.orgchart .isSiblingsCollapsed:not(.left-sibs):not(.right-sibs)::before,
.orgchart .isSiblingsCollapsed.left-sibs.right-sibs::before,
.orgchart .isSiblingsCollapsed.right-sibs:first-child:before,
.orgchart .isSiblingsCollapsed.left-sibs:last-child:before,
.orgchart .isCollapsedSibling::before,
.orgchart .isCollapsedSibling .hierarchy::before,
.orgchart .isChildrenCollapsed > .node:not(:only-child)::after,
.orgchart .isCollapsedDescendant::before,
.orgchart .isCollapsedDescendant > .node::before,
.orgchart .isCollapsedDescendant > .node::after,
.orgchart .isAncestorsCollapsed:only-child::before,
.orgchart .isAncestorsCollapsed > .node::before {
  content: none;
}

/* excluding leaf node */
.orgchart .node:not(:only-child)::after {
  content: "";
  position: absolute;
  bottom: -12px; /* -(background square size + node's border width) */
  left: calc(50% - 1px);
  width: 2px;
  height: 10px; /* background square size */
  background-color: rgba(217, 83, 79, 0.8);
}

.orgchart ul li .node.allowedDrop {
  border-color: rgba(68, 157, 68, 0.9);
}

.orgchart ul li .node.currentDropTarget {
  background-color: rgba(68, 157, 68, 0.9);
}

.orgchart ul li .node.selected {
  background-color: rgba(238, 217, 54, 0.5);
}

.orgchart ul li .node:hover {
  background-color: rgba(238, 217, 54, 0.5);
}

/* excluding root node */
.orgchart > ul > li > ul li > .node::before {
  content: "";
  position: absolute;
  top: var(--top, -12px); /* -(fallback value = background square size + border width of node) */
  left: calc(50% - 1px);
  width: 2px;
  height: var(--height, 10px); /* fallback value = background square size */
  background-color: rgba(217, 83, 79, 0.8);
}

.orgchart > ul > li > ul li.isSiblingsCollapsed > .node::before {
  top: var(--top-cross-point, -12px);
  height: var(--height-cross-point, 10px);
}

/* node styling */
.orgchart .node {
  box-sizing: border-box;
  display: inline-block;
  position: relative;
  margin: 0 0 20px 0;
  padding: 3px;
  border: 2px dashed transparent;
  text-align: center;
}

.orgchart.l2r .node,
.orgchart.r2l .node {
  width: 50px;
  height: 140px;
}

.orgchart .node:hover {
  background-color: rgba(238, 217, 54, 0.5);
  transition: 0.5s;
  cursor: default;
  z-index: 20;
}

.orgchart .node.focused {
  background-color: rgba(238, 217, 54, 0.5);
}

.orgchart .ghost-node {
  position: fixed;
  left: -10000px;
  top: -10000px;
}

.orgchart .ghost-node rect {
  fill: #ffffff;
  stroke: #bf0000;
}

.orgchart .node.allowedDrop {
  border-color: rgba(68, 157, 68, 0.9);
}

.orgchart .node > .spinner {
  position: absolute;
  top: calc(50% - 1rem);
  left: calc(50% - 1rem);
}

.orgchart .node > .spinner::before {
  width: 2rem;
  height: 2rem;
  border-width: 0.2rem;
  border-radius: 1rem;
  border-top-color: rgba(68, 157, 68, 0.8);
  border-bottom-color: rgba(68, 157, 68, 0.8);
  border-left-color: rgba(68, 157, 68, 0.8);
}

.orgchart .node .title {
  box-sizing: border-box;
  width: 130px;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  height: 20px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: rgba(217, 83, 79, 0.8);
  color: #fff;
  border-radius: 4px 4px 0 0;
}

.orgchart.b2t .node .title {
  transform: rotate(-180deg);
  transform-origin: center bottom;
}

.orgchart.l2r .node .title {
  transform: rotate(-90deg) translate(-45px, -45px) rotateY(180deg);
  transform-origin: bottom center;
}

.orgchart.r2l .node .title {
  transform: rotate(-90deg) translate(-45px, -45px);
  transform-origin: bottom center;
}

.orgchart .node .title .parentNodeSymbol {
  float: left;
}

.orgchart .node .title .parentNodeSymbol::before {
  color: #fff;
}

.orgchart .node .title .parentNodeSymbol::after {
  background-color: #fff;
}

.orgchart .node .content {
  box-sizing: border-box;
  height: 20px;
  line-height: 20px;
  font-size: 10px;
  border: 1px solid rgba(217, 83, 79, 0.8);
  border-width: 0 1px 1px 1px;
  border-radius: 0 0 0.25rem 0.25rem;
  text-align: center;
  background-color: #fff;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.orgchart.b2t .node .content {
  transform: rotate(180deg);
  transform-origin: center top;
}

.orgchart.l2r .node .content {
  transform: rotate(-90deg) translate(-45px, -45px) rotateY(180deg);
  transform-origin: top center;
  width: 130px;
}

.orgchart.r2l .node .content {
  transform: rotate(-90deg) translate(-45px, -45px);
  transform-origin: top center;
  width: 130px;
}

.orgchart .node .edge {
  position: absolute;
  cursor: default;
  transition: 0.2s;
}

.orgchart .node .edge::before {
  border-color: rgba(68, 157, 68, 0.5);
}

.orgchart.noncollapsable .node .edge {
  display: none;
}

.orgchart .node .edge:hover {
  cursor: pointer;
}

.orgchart .edge:hover::before {
  border-color: #449d44;
}

.orgchart .node .verticalEdge {
  width: calc(100% - 6px); /* node top half's width */
  height: 10px; /* background square's size */
  left: 3px; /* node's padding value */
}

.orgchart .node .verticalEdge::before {
  position: absolute;
  left: calc(50% - 5px); /* 50% width of node - half width of up arrow icon) */
}

.orgchart .node .topEdge {
  top: -2px;
}

.orgchart .node .topEdge.oci-chevron-up::before {
  top: 2px;
}

.orgchart .node .topEdge.oci-chevron-down::before {
  bottom: 3px;
}

.orgchart .node .bottomEdge {
  bottom: -2px; /* -(node's border-width) */
}

.orgchart .node .bottomEdge.oci-chevron-up::before {
  bottom: -3px;
}

.orgchart .node .bottomEdge.oci-chevron-down::before {
  bottom: 1px;
}

.orgchart .node .horizontalEdge {
  width: 10px;
  height: calc(100% - 6px);
  top: 3px; /* node's padding */
}

.orgchart .node .rightEdge {
  right: -2px;
}

.orgchart .node .leftEdge {
  left: -2px;
}

.orgchart .node .horizontalEdge::before {
  position: absolute;
  top: calc(50% - 5px);
}

.orgchart .node .leftEdge.oci-chevron-right::before {
  left: -3px;
}

.orgchart .node .leftEdge.oci-chevron-left::before {
  left: 1px;
}

.orgchart .node .rightEdge.oci-chevron-left::before {
  right: -3px;
}

.orgchart .node .rightEdge.oci-chevron-right::before {
  right: 1px;
}

.orgchart .node .toggleBtn {
  position: absolute;
  left: -2px; /* -(border width of node) */
  bottom: -2px;
  width: 16px;
  height: 16px;
}

.orgchart .node .toggleBtn::before {
  background-color: rgba(68, 157, 68, 0.6);
  position: absolute;
  left: 0;
  bottom: 0;
}

.orgchart .node .toggleBtn:hover::before {
  background-color: #449d44;
}

.oc-export-btn {
  margin-left: 0.5rem;
  padding: 0.5rem 1rem;
}

.orgchart .node {
  transition: transform 0.3s, opacity 0.3s;
}

.orgchart .slide-down {
  opacity: 0;
  transform: translateY(40px);
}

.orgchart.l2r .node.slide-down,
.orgchart.r2l .node.slide-down {
  transform: translateY(130px);
}

.orgchart .slide-up {
  opacity: 0;
  transform: translateY(-40px);
}

.orgchart.l2r .node.slide-up,
.orgchart.r2l .node.slide-up {
  transform: translateY(-130px);
}

.orgchart .slide-right {
  opacity: 0;
  transform: translateX(130px);
}

.orgchart.l2r .node.slide-right,
.orgchart.r2l .node.slide-right {
  transform: translateX(40px);
}

.orgchart .slide-left {
  opacity: 0;
  transform: translateX(-130px);
}

.orgchart.l2r .node.slide-left,
.orgchart.r2l .node.slide-left {
  transform: translateX(-40px);
}

/* styles for vertical nodes */
.orgchart .nodes.vertical {
  display: block;
  padding-left: 10px; /* width of background square*/
}

.orgchart .nodes.vertical .nodes {
  list-style: none;
  display: block;
  margin: 0;
  padding-left: 10px; /* width of background square*/
  text-align: left;
}

.orgchart .nodes.vertical .node {
  margin-bottom: 0;
}

.orgchart .nodes.vertical .node::before,
.orgchart .nodes.vertical .node::after {
  content: none;
}

.orgchart .nodes.vertical .hierarchy {
  position: relative;
  text-align: left;
}

.orgchart .nodes.vertical .hierarchy::before,
.orgchart .nodes.vertical .hierarchy::after {
  box-sizing: border-box;
  content: "";
  position: absolute;
  left: -6px; /* -(background square size + half width of line */
  border-color: rgba(217, 83, 79, 0.8);
  border-style: solid;
  border-width: 0 0 2px 2px;
}

.orgchart .nodes.vertical .hierarchy::before {
  top: 0px;
  height: 26px; /* node top half's height(25px) + half width of line */
  width: 11px; /* background square size + half width of line */
}

.orgchart .nodes.vertical .hierarchy::after {
  bottom: 0;
  height: calc(100% - 24px); /* height of hierarchy - (node top half's height(25px) - half width of line) */
}

.orgchart .nodes.vertical .hierarchy:last-child::after {
  border-width: 2px 0 0 0;
}

.orgchart .nodes.vertical > .hierarchy:first-child::before {
  box-sizing: border-box;
  top: -11px; /* -(background square size + half width of line) */
  height: 35px; /* node top half's height + node padding + node border width + background square size */
  width: calc(50% + 2px);
  border-width: 2px 0 0 2px;
}

.orgchart .nodes.vertical > .hierarchy:first-child::after {
  box-sizing: border-box;
  top: 24px; /* node bottom half's height(25px) - half border width of line */
  width: 11px; /* backgroud square size + half border width of line */
  border-width: 2px 0 0 2px;
}

.orgchart .nodes.vertical > .hierarchy:first-child:last-child::after {
  border-width: 2px 0 0 0;
}

/* custom icons for orgchart */
.oci {
  display: inline-block;
  position: relative;
  font-style: normal;
  font-family: Arial;
}

.oci-menu::before {
  content: "≡";
  display: inline-block;
  width: 1rem;
  height: 1rem;
  text-align: center;
  line-height: 1rem;
  color: #000;
  font-size: 1rem;
}

.oci-chevron-up::before {
  content: "";
  box-sizing: border-box;
  width: 10px;
  height: 10px;
  display: inline-block;
  border: 3px solid #000;
  transform: rotate(45deg);
  border-right: unset;
  border-bottom: unset;
}

.oci-chevron-down::before {
  content: "";
  box-sizing: border-box;
  width: 10px;
  height: 10px;
  display: inline-block;
  border: 3px solid #000;
  transform: rotate(45deg);
  border-top: unset;
  border-left: unset;
}

.oci-chevron-left::before {
  content: "";
  box-sizing: border-box;
  width: 10px;
  height: 10px;
  display: inline-block;
  border: 3px solid #000;
  transform: rotate(45deg);
  border-top: unset;
  border-right: unset;
}

.oci-chevron-right::before {
  content: "";
  box-sizing: border-box;
  width: 10px;
  height: 10px;
  display: inline-block;
  border: 3px solid #000;
  transform: rotate(45deg);
  border-left: unset;
  border-bottom: unset;
}

.oci-plus-square::before {
  content: "﹢";
  display: inline-block;
  width: 16px;
  height: 16px;
  text-align: center;
  line-height: 16px;
  background-color: #000;
  color: #fff;
  font-weight: bold;
}

.oci-minus-square::before {
  content: "﹣";
  display: inline-block;
  width: 16px;
  height: 16px;
  text-align: center;
  line-height: 16px;
  background-color: #000;
  color: #fff;
  font-weight: bold;
}

.oci-arrow-square-up::before {
  content: "⬆";
  display: inline-block;
  width: 1rem;
  height: 1rem;
  text-align: center;
  line-height: 1rem;
  background-color: #000;
  color: #fff;
  font-weight: bold;
}

.oci-arrow-square-down::before {
  content: "⬇";
  display: inline-block;
  width: 1rem;
  height: 1rem;
  text-align: center;
  line-height: 1rem;
  background-color: #000;
  color: #fff;
  font-weight: bold;
}

.oci-info-circle::before {
  content: "i";
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border-radius: 0.5rem;
  background-color: #000;
  color: #fff;
  text-align: center;
  font-weight: bold;
}

.oci-spinner::before {
  content: "";
  vertical-align: text-bottom;
  display: inline-block;
  box-sizing: border-box;
  width: 1rem;
  height: 1rem;
  border: 0.1rem solid #000;
  border-right-color: transparent;
  border-radius: 0.625rem;
  animation: oci-infinite-spinning 0.75s linear infinite;
}

@keyframes oci-infinite-spinning {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
#chart-container {
  position: relative;
  margin: 0.5rem;
  overflow: auto;
  text-align: center;
}

.orgchart .node .title,
.orgchart .node .content {
  border-radius: 0;
}

.orgchart .node .title .parentNodeSymbol {
  display: none;
}

.orgchart {
  background: none;
}

.orgchart td.left,
.orgchart td.right,
.orgchart td.top {
  border-color: #aaa;
}

.orgchart td > .down {
  background-color: #aaa;
}

.orgchart .middle-level .title {
  background-color: hsl(198, 100%, 29%);
}

.orgchart .middle-level .content {
  border-color: hsl(198, 100%, 29%);
}

.orgchart .product-dept .title {
  background-color: hsl(193, 100%, 39%);
}

.orgchart .product-dept .content {
  border-color: hsl(193, 100%, 39%);
}

.orgchart .rd-dept .title {
  background-color: hsl(12, 19%, 69%);
}

.orgchart .rd-dept .content {
  border-color: hsl(12, 19%, 69%);
}

.orgchart .pipeline1 .title {
  background-color: hsl(12, 17%, 29%);
}

.orgchart .pipeline1 .content {
  border-color: hsl(12, 17%, 29%);
}

.orgchart .frontend1 .title {
  background-color: hsl(8, 77%, 56%);
}

.orgchart .frontend1 .content {
  border-color: hsl(8, 77%, 56%);
}

/*!
 * Bootstrap v5.0.2 (https://getbootstrap.com/)
 * Copyright 2011-2021 The Bootstrap Authors
 * Copyright 2011-2021 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
:root {
  --bs-blue: hsl(204, 70%, 53%);
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #dc3545;
  --bs-orange: hsl(19, 85%, 63%);
  --bs-yellow: hsl(40, 91%, 60%);
  --bs-green: hsl(121, 47%, 61%);
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-white: hsl(0, 0%, 100%);
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-primary: hsl(204, 70%, 53%);
  --bs-secondary: #6c757d;
  --bs-success: hsl(121, 47%, 61%);
  --bs-info: #0dcaf0;
  --bs-warning: hsl(40, 91%, 60%);
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth;
  }
}

body {
  margin: 0;
  font-family: var(--bs-font-sans-serif);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: hsl(0, 0%, 100%);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

hr {
  margin: 1rem 0;
  color: inherit;
  background-color: currentColor;
  border: 0;
  opacity: 0.25;
}

hr:not([size]) {
  height: 1px;
}

h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}

h1, .h1 {
  font-size: calc(1.375rem + 1.5vw);
}
@media (min-width: 1200px) {
  h1, .h1 {
    font-size: 2.5rem;
  }
}

h2, .h2 {
  font-size: calc(1.325rem + 0.9vw);
}
@media (min-width: 1200px) {
  h2, .h2 {
    font-size: 2rem;
  }
}

h3, .h3 {
  font-size: calc(1.3rem + 0.6vw);
}
@media (min-width: 1200px) {
  h3, .h3 {
    font-size: 1.75rem;
  }
}

h4, .h4 {
  font-size: calc(1.275rem + 0.3vw);
}
@media (min-width: 1200px) {
  h4, .h4 {
    font-size: 1.5rem;
  }
}

h5, .h5 {
  font-size: 1.25rem;
}

h6, .h6 {
  font-size: 1rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title],
abbr[data-bs-original-title] {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  cursor: help;
  -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul {
  padding-left: 2rem;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: bolder;
}

small, .small {
  font-size: 0.875em;
}

mark, .mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: hsl(204, 70%, 53%);
  text-decoration: underline;
}
a:hover {
  color: rgb(41.004, 121.5432, 175.236);
}

a:not([href]):not([class]), a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: var(--bs-font-monospace);
  font-size: 1em;
  direction: ltr /* rtl:ignore */;
  unicode-bidi: bidi-override;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 0.875em;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

code {
  font-size: 0.875em;
  color: #d63384;
  word-wrap: break-word;
}
a > code {
  color: inherit;
}

kbd {
  padding: 0.2rem 0.4rem;
  font-size: 0.875em;
  color: hsl(0, 0%, 100%);
  background-color: #212529;
  border-radius: 0.2rem;
}
kbd kbd {
  padding: 0;
  font-size: 1em;
  font-weight: 700;
}

figure {
  margin: 0 0 1rem;
}

img,
svg {
  vertical-align: middle;
}

table {
  caption-side: bottom;
  border-collapse: collapse;
}

caption {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: #6c757d;
  text-align: left;
}

th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}

thead,
tbody,
tfoot,
tr,
td,
th {
  border-color: inherit;
  border-style: solid;
  border-width: 0;
}

label {
  display: inline-block;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible) {
  outline: 0;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}
select:disabled {
  opacity: 1;
}

[list]::-webkit-calendar-picker-indicator {
  display: none;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}
button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

textarea {
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  float: left;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit;
}
@media (min-width: 1200px) {
  legend {
    font-size: 1.5rem;
  }
}
legend + * {
  clear: left;
}

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
  padding: 0;
}

::-webkit-inner-spin-button {
  height: auto;
}

[type=search] {
  outline-offset: -2px;
  -webkit-appearance: textfield;
}

/* rtl:raw:
[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-color-swatch-wrapper {
  padding: 0;
}

::file-selector-button {
  font: inherit;
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

iframe {
  border: 0;
}

summary {
  display: list-item;
  cursor: pointer;
}

progress {
  vertical-align: baseline;
}

[hidden] {
  display: none !important;
}

.lead {
  font-size: 1.25rem;
  font-weight: 300;
}

.display-1 {
  font-size: calc(1.625rem + 4.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-1 {
    font-size: 5rem;
  }
}

.display-2 {
  font-size: calc(1.575rem + 3.9vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-2 {
    font-size: 4.5rem;
  }
}

.display-3 {
  font-size: calc(1.525rem + 3.3vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-3 {
    font-size: 4rem;
  }
}

.display-4 {
  font-size: calc(1.475rem + 2.7vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-4 {
    font-size: 3.5rem;
  }
}

.display-5 {
  font-size: calc(1.425rem + 2.1vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-5 {
    font-size: 3rem;
  }
}

.display-6 {
  font-size: calc(1.375rem + 1.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-6 {
    font-size: 2.5rem;
  }
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.list-inline {
  padding-left: 0;
  list-style: none;
}

.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

.initialism {
  font-size: 0.875em;
  text-transform: uppercase;
}

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}
.blockquote > :last-child {
  margin-bottom: 0;
}

.blockquote-footer {
  margin-top: -1rem;
  margin-bottom: 1rem;
  font-size: 0.875em;
  color: #6c757d;
}
.blockquote-footer::before {
  content: "— ";
}

.container,
.container-fluid,
.container-xxl,
.container-xl,
.container-lg,
.container-md,
.container-sm {
  width: 100%;
  padding-right: var(--bs-gutter-x, 0.75rem);
  padding-left: var(--bs-gutter-x, 0.75rem);
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container-sm, .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container-md, .container-sm, .container {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .container-lg, .container-md, .container-sm, .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1140px;
  }
}
@media (min-width: 1400px) {
  .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1320px;
  }
}
.row {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(var(--bs-gutter-y) * -1);
  margin-right: calc(var(--bs-gutter-x) * -0.5);
  margin-left: calc(var(--bs-gutter-x) * -0.5);
}
.row > * {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}

.col {
  flex: 1 0 0%;
}

.row-cols-auto > * {
  flex: 0 0 auto;
  width: auto;
}

.row-cols-1 > * {
  flex: 0 0 auto;
  width: 100%;
}

.row-cols-2 > * {
  flex: 0 0 auto;
  width: 50%;
}

.row-cols-3 > * {
  flex: 0 0 auto;
  width: 33.3333333333%;
}

.row-cols-4 > * {
  flex: 0 0 auto;
  width: 25%;
}

.row-cols-5 > * {
  flex: 0 0 auto;
  width: 20%;
}

.row-cols-6 > * {
  flex: 0 0 auto;
  width: 16.6666666667%;
}

@media (min-width: 576px) {
  .col-sm {
    flex: 1 0 0%;
  }
  .row-cols-sm-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-sm-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-sm-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-sm-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-sm-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-sm-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-sm-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex: 1 0 0%;
  }
  .row-cols-md-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-md-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-md-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-md-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-md-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-md-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex: 1 0 0%;
  }
  .row-cols-lg-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-lg-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-lg-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-lg-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-lg-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-lg-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-lg-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex: 1 0 0%;
  }
  .row-cols-xl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-xl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-xl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-xl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-xl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-xl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
}
@media (min-width: 1400px) {
  .col-xxl {
    flex: 1 0 0%;
  }
  .row-cols-xxl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-xxl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-xxl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-xxl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-xxl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-xxl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xxl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
}
.col-auto {
  flex: 0 0 auto;
  width: auto;
}

.col-1 {
  flex: 0 0 auto;
  width: 8.33333333%;
}

.col-2 {
  flex: 0 0 auto;
  width: 16.66666667%;
}

.col-3 {
  flex: 0 0 auto;
  width: 25%;
}

.col-4 {
  flex: 0 0 auto;
  width: 33.33333333%;
}

.col-5 {
  flex: 0 0 auto;
  width: 41.66666667%;
}

.col-6 {
  flex: 0 0 auto;
  width: 50%;
}

.col-7 {
  flex: 0 0 auto;
  width: 58.33333333%;
}

.col-8 {
  flex: 0 0 auto;
  width: 66.66666667%;
}

.col-9 {
  flex: 0 0 auto;
  width: 75%;
}

.col-10 {
  flex: 0 0 auto;
  width: 83.33333333%;
}

.col-11 {
  flex: 0 0 auto;
  width: 91.66666667%;
}

.col-12 {
  flex: 0 0 auto;
  width: 100%;
}

.offset-1 {
  margin-left: 8.33333333%;
}

.offset-2 {
  margin-left: 16.66666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.33333333%;
}

.offset-5 {
  margin-left: 41.66666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.33333333%;
}

.offset-8 {
  margin-left: 66.66666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.33333333%;
}

.offset-11 {
  margin-left: 91.66666667%;
}

.g-0,
.gx-0 {
  --bs-gutter-x: 0;
}

.g-0,
.gy-0 {
  --bs-gutter-y: 0;
}

.g-1,
.gx-1 {
  --bs-gutter-x: 0.25rem;
}

.g-1,
.gy-1 {
  --bs-gutter-y: 0.25rem;
}

.g-2,
.gx-2 {
  --bs-gutter-x: 0.5rem;
}

.g-2,
.gy-2 {
  --bs-gutter-y: 0.5rem;
}

.g-3,
.gx-3 {
  --bs-gutter-x: 1rem;
}

.g-3,
.gy-3 {
  --bs-gutter-y: 1rem;
}

.g-4,
.gx-4 {
  --bs-gutter-x: 1.5rem;
}

.g-4,
.gy-4 {
  --bs-gutter-y: 1.5rem;
}

.g-5,
.gx-5 {
  --bs-gutter-x: 3rem;
}

.g-5,
.gy-5 {
  --bs-gutter-y: 3rem;
}

@media (min-width: 576px) {
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-sm-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-sm-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-sm-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-sm-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-sm-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-sm-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-sm-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-sm-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-sm-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-sm-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-sm-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-sm-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.33333333%;
  }
  .offset-sm-2 {
    margin-left: 16.66666667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.33333333%;
  }
  .offset-sm-5 {
    margin-left: 41.66666667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.33333333%;
  }
  .offset-sm-8 {
    margin-left: 66.66666667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.33333333%;
  }
  .offset-sm-11 {
    margin-left: 91.66666667%;
  }
  .g-sm-0,
  .gx-sm-0 {
    --bs-gutter-x: 0;
  }
  .g-sm-0,
  .gy-sm-0 {
    --bs-gutter-y: 0;
  }
  .g-sm-1,
  .gx-sm-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-sm-1,
  .gy-sm-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-sm-2,
  .gx-sm-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-sm-2,
  .gy-sm-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-sm-3,
  .gx-sm-3 {
    --bs-gutter-x: 1rem;
  }
  .g-sm-3,
  .gy-sm-3 {
    --bs-gutter-y: 1rem;
  }
  .g-sm-4,
  .gx-sm-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-sm-4,
  .gy-sm-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-sm-5,
  .gx-sm-5 {
    --bs-gutter-x: 3rem;
  }
  .g-sm-5,
  .gy-sm-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 768px) {
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-md-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-md-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-md-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-md-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-md-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-md-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-md-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-md-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-md-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-md-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.33333333%;
  }
  .offset-md-2 {
    margin-left: 16.66666667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.33333333%;
  }
  .offset-md-5 {
    margin-left: 41.66666667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.33333333%;
  }
  .offset-md-8 {
    margin-left: 66.66666667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.33333333%;
  }
  .offset-md-11 {
    margin-left: 91.66666667%;
  }
  .g-md-0,
  .gx-md-0 {
    --bs-gutter-x: 0;
  }
  .g-md-0,
  .gy-md-0 {
    --bs-gutter-y: 0;
  }
  .g-md-1,
  .gx-md-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-md-1,
  .gy-md-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-md-2,
  .gx-md-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-md-2,
  .gy-md-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-md-3,
  .gx-md-3 {
    --bs-gutter-x: 1rem;
  }
  .g-md-3,
  .gy-md-3 {
    --bs-gutter-y: 1rem;
  }
  .g-md-4,
  .gx-md-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-md-4,
  .gy-md-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-md-5,
  .gx-md-5 {
    --bs-gutter-x: 3rem;
  }
  .g-md-5,
  .gy-md-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 992px) {
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-lg-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-lg-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-lg-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-lg-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-lg-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-lg-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-lg-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-lg-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-lg-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-lg-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-lg-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.33333333%;
  }
  .offset-lg-2 {
    margin-left: 16.66666667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.33333333%;
  }
  .offset-lg-5 {
    margin-left: 41.66666667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.33333333%;
  }
  .offset-lg-8 {
    margin-left: 66.66666667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.33333333%;
  }
  .offset-lg-11 {
    margin-left: 91.66666667%;
  }
  .g-lg-0,
  .gx-lg-0 {
    --bs-gutter-x: 0;
  }
  .g-lg-0,
  .gy-lg-0 {
    --bs-gutter-y: 0;
  }
  .g-lg-1,
  .gx-lg-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-lg-1,
  .gy-lg-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-lg-2,
  .gx-lg-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-lg-2,
  .gy-lg-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-lg-3,
  .gx-lg-3 {
    --bs-gutter-x: 1rem;
  }
  .g-lg-3,
  .gy-lg-3 {
    --bs-gutter-y: 1rem;
  }
  .g-lg-4,
  .gx-lg-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-lg-4,
  .gy-lg-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-lg-5,
  .gx-lg-5 {
    --bs-gutter-x: 3rem;
  }
  .g-lg-5,
  .gy-lg-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 1200px) {
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-xl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-xl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-xl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-xl-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-xl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-xl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-xl-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.33333333%;
  }
  .offset-xl-2 {
    margin-left: 16.66666667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.33333333%;
  }
  .offset-xl-5 {
    margin-left: 41.66666667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.33333333%;
  }
  .offset-xl-8 {
    margin-left: 66.66666667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.33333333%;
  }
  .offset-xl-11 {
    margin-left: 91.66666667%;
  }
  .g-xl-0,
  .gx-xl-0 {
    --bs-gutter-x: 0;
  }
  .g-xl-0,
  .gy-xl-0 {
    --bs-gutter-y: 0;
  }
  .g-xl-1,
  .gx-xl-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-xl-1,
  .gy-xl-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-xl-2,
  .gx-xl-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-xl-2,
  .gy-xl-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-xl-3,
  .gx-xl-3 {
    --bs-gutter-x: 1rem;
  }
  .g-xl-3,
  .gy-xl-3 {
    --bs-gutter-y: 1rem;
  }
  .g-xl-4,
  .gx-xl-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-xl-4,
  .gy-xl-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-xl-5,
  .gx-xl-5 {
    --bs-gutter-x: 3rem;
  }
  .g-xl-5,
  .gy-xl-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 1400px) {
  .col-xxl-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-xxl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-xxl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xxl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xxl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xxl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xxl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xxl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-xxl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-xxl-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-xxl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-xxl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-xxl-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-xxl-0 {
    margin-left: 0;
  }
  .offset-xxl-1 {
    margin-left: 8.33333333%;
  }
  .offset-xxl-2 {
    margin-left: 16.66666667%;
  }
  .offset-xxl-3 {
    margin-left: 25%;
  }
  .offset-xxl-4 {
    margin-left: 33.33333333%;
  }
  .offset-xxl-5 {
    margin-left: 41.66666667%;
  }
  .offset-xxl-6 {
    margin-left: 50%;
  }
  .offset-xxl-7 {
    margin-left: 58.33333333%;
  }
  .offset-xxl-8 {
    margin-left: 66.66666667%;
  }
  .offset-xxl-9 {
    margin-left: 75%;
  }
  .offset-xxl-10 {
    margin-left: 83.33333333%;
  }
  .offset-xxl-11 {
    margin-left: 91.66666667%;
  }
  .g-xxl-0,
  .gx-xxl-0 {
    --bs-gutter-x: 0;
  }
  .g-xxl-0,
  .gy-xxl-0 {
    --bs-gutter-y: 0;
  }
  .g-xxl-1,
  .gx-xxl-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-xxl-1,
  .gy-xxl-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-xxl-2,
  .gx-xxl-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-xxl-2,
  .gy-xxl-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-xxl-3,
  .gx-xxl-3 {
    --bs-gutter-x: 1rem;
  }
  .g-xxl-3,
  .gy-xxl-3 {
    --bs-gutter-y: 1rem;
  }
  .g-xxl-4,
  .gx-xxl-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-xxl-4,
  .gy-xxl-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-xxl-5,
  .gx-xxl-5 {
    --bs-gutter-x: 3rem;
  }
  .g-xxl-5,
  .gy-xxl-5 {
    --bs-gutter-y: 3rem;
  }
}
.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.link-primary {
  color: hsl(204, 70%, 53%);
}
.link-primary:hover, .link-primary:focus {
  color: rgb(92.004, 172.5432, 226.236);
}

.link-secondary {
  color: #6c757d;
}
.link-secondary:hover, .link-secondary:focus {
  color: rgb(86.4, 93.6, 100);
}

.link-success {
  color: hsl(121, 47%, 61%);
}
.link-success:hover, .link-success:focus {
  color: rgb(138.0468, 212.8332, 139.29324);
}

.link-info {
  color: #0dcaf0;
}
.link-info:hover, .link-info:focus {
  color: rgb(61.4, 212.6, 243);
}

.link-warning {
  color: hsl(40, 91%, 60%);
}
.link-warning:hover, .link-warning:focus {
  color: rgb(247.656, 198.152, 99.144);
}

.link-danger {
  color: #dc3545;
}
.link-danger:hover, .link-danger:focus {
  color: rgb(176, 42.4, 55.2);
}

.link-light {
  color: #f8f9fa;
}
.link-light:hover, .link-light:focus {
  color: rgb(249.4, 250.2, 251);
}

.link-dark {
  color: #212529;
}
.link-dark:hover, .link-dark:focus {
  color: rgb(26.4, 29.6, 32.8);
}

.ratio {
  position: relative;
  width: 100%;
}
.ratio::before {
  display: block;
  padding-top: var(--bs-aspect-ratio);
  content: "";
}
.ratio > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ratio-1x1 {
  --bs-aspect-ratio: 100%;
}

.ratio-4x3 {
  --bs-aspect-ratio: 75%;
}

.ratio-16x9 {
  --bs-aspect-ratio: 56.25%;
}

.ratio-21x9 {
  --bs-aspect-ratio: 42.8571428571%;
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 1020;
}

@media (min-width: 576px) {
  .sticky-sm-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 768px) {
  .sticky-md-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 992px) {
  .sticky-lg-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 1200px) {
  .sticky-xl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 1400px) {
  .sticky-xxl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  content: "";
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.float-start {
  float: left !important;
}

.float-end {
  float: right !important;
}

.float-none {
  float: none !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-visible {
  overflow: visible !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-grid {
  display: grid !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

.d-none {
  display: none !important;
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.shadow-none {
  box-shadow: none !important;
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: sticky !important;
}

.top-0 {
  top: 0 !important;
}

.top-50 {
  top: 50% !important;
}

.top-100 {
  top: 100% !important;
}

.bottom-0 {
  bottom: 0 !important;
}

.bottom-50 {
  bottom: 50% !important;
}

.bottom-100 {
  bottom: 100% !important;
}

.start-0 {
  left: 0 !important;
}

.start-50 {
  left: 50% !important;
}

.start-100 {
  left: 100% !important;
}

.end-0 {
  right: 0 !important;
}

.end-50 {
  right: 50% !important;
}

.end-100 {
  right: 100% !important;
}

.translate-middle {
  transform: translate(-50%, -50%) !important;
}

.translate-middle-x {
  transform: translateX(-50%) !important;
}

.translate-middle-y {
  transform: translateY(-50%) !important;
}

.border {
  border: 1px solid hsl(213, 22%, 84%) !important;
}

.border-0 {
  border: 0 !important;
}

.border-top {
  border-top: 1px solid hsl(213, 22%, 84%) !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-end {
  border-right: 1px solid hsl(213, 22%, 84%) !important;
}

.border-end-0 {
  border-right: 0 !important;
}

.border-bottom {
  border-bottom: 1px solid hsl(213, 22%, 84%) !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-start {
  border-left: 1px solid hsl(213, 22%, 84%) !important;
}

.border-start-0 {
  border-left: 0 !important;
}

.border-primary {
  border-color: hsl(204, 70%, 53%) !important;
}

.border-secondary {
  border-color: #6c757d !important;
}

.border-success {
  border-color: hsl(121, 47%, 61%) !important;
}

.border-info {
  border-color: #0dcaf0 !important;
}

.border-warning {
  border-color: hsl(40, 91%, 60%) !important;
}

.border-danger {
  border-color: #dc3545 !important;
}

.border-light {
  border-color: #f8f9fa !important;
}

.border-dark {
  border-color: #212529 !important;
}

.border-white {
  border-color: hsl(0, 0%, 100%) !important;
}

.border-1 {
  border-width: 1px !important;
}

.border-2 {
  border-width: 2px !important;
}

.border-3 {
  border-width: 3px !important;
}

.border-4 {
  border-width: 4px !important;
}

.border-5 {
  border-width: 5px !important;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.mw-100 {
  max-width: 100% !important;
}

.vw-100 {
  width: 100vw !important;
}

.min-vw-100 {
  min-width: 100vw !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.mh-100 {
  max-height: 100% !important;
}

.vh-100 {
  height: 100vh !important;
}

.min-vh-100 {
  min-height: 100vh !important;
}

.flex-fill {
  flex: 1 1 auto !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.gap-0 {
  gap: 0 !important;
}

.gap-1 {
  gap: 0.25rem !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

.gap-3 {
  gap: 1rem !important;
}

.gap-4 {
  gap: 1.5rem !important;
}

.gap-5 {
  gap: 3rem !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.justify-content-evenly {
  justify-content: space-evenly !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

.order-first {
  order: -1 !important;
}

.order-0 {
  order: 0 !important;
}

.order-1 {
  order: 1 !important;
}

.order-2 {
  order: 2 !important;
}

.order-3 {
  order: 3 !important;
}

.order-4 {
  order: 4 !important;
}

.order-5 {
  order: 5 !important;
}

.order-last {
  order: 6 !important;
}

.m-0 {
  margin: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.m-5 {
  margin: 3rem !important;
}

.m-auto {
  margin: auto !important;
}

.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

.mx-1 {
  margin-right: 0.25rem !important;
  margin-left: 0.25rem !important;
}

.mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important;
}

.mx-3 {
  margin-right: 1rem !important;
  margin-left: 1rem !important;
}

.mx-4 {
  margin-right: 1.5rem !important;
  margin-left: 1.5rem !important;
}

.mx-5 {
  margin-right: 3rem !important;
  margin-left: 3rem !important;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.my-1 {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
}

.my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}

.my-3 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

.my-4 {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

.my-5 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mt-1 {
  margin-top: 0.25rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.mt-5 {
  margin-top: 3rem !important;
}

.mt-auto {
  margin-top: auto !important;
}

.me-0 {
  margin-right: 0 !important;
}

.me-1 {
  margin-right: 0.25rem !important;
}

.me-2 {
  margin-right: 0.5rem !important;
}

.me-3 {
  margin-right: 1rem !important;
}

.me-4 {
  margin-right: 1.5rem !important;
}

.me-5 {
  margin-right: 3rem !important;
}

.me-auto {
  margin-right: auto !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

.mb-5 {
  margin-bottom: 3rem !important;
}

.mb-auto {
  margin-bottom: auto !important;
}

.ms-0 {
  margin-left: 0 !important;
}

.ms-1 {
  margin-left: 0.25rem !important;
}

.ms-2 {
  margin-left: 0.5rem !important;
}

.ms-3 {
  margin-left: 1rem !important;
}

.ms-4 {
  margin-left: 1.5rem !important;
}

.ms-5 {
  margin-left: 3rem !important;
}

.ms-auto {
  margin-left: auto !important;
}

.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.p-5 {
  padding: 3rem !important;
}

.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.px-1 {
  padding-right: 0.25rem !important;
  padding-left: 0.25rem !important;
}

.px-2 {
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important;
}

.px-3 {
  padding-right: 1rem !important;
  padding-left: 1rem !important;
}

.px-4 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important;
}

.px-5 {
  padding-right: 3rem !important;
  padding-left: 3rem !important;
}

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}

.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.py-3 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.py-5 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pt-1 {
  padding-top: 0.25rem !important;
}

.pt-2 {
  padding-top: 0.5rem !important;
}

.pt-3 {
  padding-top: 1rem !important;
}

.pt-4 {
  padding-top: 1.5rem !important;
}

.pt-5 {
  padding-top: 3rem !important;
}

.pe-0 {
  padding-right: 0 !important;
}

.pe-1 {
  padding-right: 0.25rem !important;
}

.pe-2 {
  padding-right: 0.5rem !important;
}

.pe-3 {
  padding-right: 1rem !important;
}

.pe-4 {
  padding-right: 1.5rem !important;
}

.pe-5 {
  padding-right: 3rem !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pb-1 {
  padding-bottom: 0.25rem !important;
}

.pb-2 {
  padding-bottom: 0.5rem !important;
}

.pb-3 {
  padding-bottom: 1rem !important;
}

.pb-4 {
  padding-bottom: 1.5rem !important;
}

.pb-5 {
  padding-bottom: 3rem !important;
}

.ps-0 {
  padding-left: 0 !important;
}

.ps-1 {
  padding-left: 0.25rem !important;
}

.ps-2 {
  padding-left: 0.5rem !important;
}

.ps-3 {
  padding-left: 1rem !important;
}

.ps-4 {
  padding-left: 1.5rem !important;
}

.ps-5 {
  padding-left: 3rem !important;
}

.font-monospace {
  font-family: var(--bs-font-monospace) !important;
}

.fs-1 {
  font-size: calc(1.375rem + 1.5vw) !important;
}

.fs-2 {
  font-size: calc(1.325rem + 0.9vw) !important;
}

.fs-3 {
  font-size: calc(1.3rem + 0.6vw) !important;
}

.fs-4 {
  font-size: calc(1.275rem + 0.3vw) !important;
}

.fs-5 {
  font-size: 1.25rem !important;
}

.fs-6 {
  font-size: 1rem !important;
}

.fst-italic {
  font-style: italic !important;
}

.fst-normal {
  font-style: normal !important;
}

.fw-light {
  font-weight: 300 !important;
}

.fw-lighter {
  font-weight: lighter !important;
}

.fw-normal {
  font-weight: 400 !important;
}

.fw-bold {
  font-weight: 700 !important;
}

.fw-bolder {
  font-weight: bolder !important;
}

.lh-1 {
  line-height: 1 !important;
}

.lh-sm {
  line-height: 1.25 !important;
}

.lh-base {
  line-height: 1.5 !important;
}

.lh-lg {
  line-height: 2 !important;
}

.text-start {
  text-align: left !important;
}

.text-end {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-decoration-underline {
  text-decoration: underline !important;
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

/* rtl:begin:remove */
.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* rtl:end:remove */
.text-primary {
  color: hsl(204, 70%, 53%) !important;
}

.text-secondary {
  color: #6c757d !important;
}

.text-success {
  color: hsl(121, 47%, 61%) !important;
}

.text-info {
  color: #0dcaf0 !important;
}

.text-warning {
  color: hsl(40, 91%, 60%) !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-light {
  color: #f8f9fa !important;
}

.text-dark {
  color: #212529 !important;
}

.text-white {
  color: hsl(0, 0%, 100%) !important;
}

.text-body {
  color: #212529 !important;
}

.text-muted {
  color: #6c757d !important;
}

.text-black-50 {
  color: rgba(0, 0, 0, 0.5) !important;
}

.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-reset {
  color: inherit !important;
}

.bg-primary {
  background-color: hsl(204, 70%, 53%) !important;
}

.bg-secondary {
  background-color: #6c757d !important;
}

.bg-success {
  background-color: hsl(121, 47%, 61%) !important;
}

.bg-info {
  background-color: #0dcaf0 !important;
}

.bg-warning {
  background-color: hsl(40, 91%, 60%) !important;
}

.bg-danger {
  background-color: #dc3545 !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

.bg-dark {
  background-color: #212529 !important;
}

.bg-body {
  background-color: hsl(0, 0%, 100%) !important;
}

.bg-white {
  background-color: hsl(0, 0%, 100%) !important;
}

.bg-transparent {
  background-color: transparent !important;
}

.bg-gradient {
  background-image: var(--bs-gradient) !important;
}

.user-select-all {
  -webkit-user-select: all !important;
     -moz-user-select: all !important;
          user-select: all !important;
}

.user-select-auto {
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
          user-select: auto !important;
}

.user-select-none {
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
          user-select: none !important;
}

.pe-none {
  pointer-events: none !important;
}

.pe-auto {
  pointer-events: auto !important;
}

.rounded {
  border-radius: 0.25rem !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.rounded-1 {
  border-radius: 0.2rem !important;
}

.rounded-2 {
  border-radius: 0.25rem !important;
}

.rounded-3 {
  border-radius: 0.3rem !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: 50rem !important;
}

.rounded-top {
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}

.rounded-end {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}

.rounded-bottom {
  border-bottom-right-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

.rounded-start {
  border-bottom-left-radius: 0.25rem !important;
  border-top-left-radius: 0.25rem !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

@media (min-width: 576px) {
  .float-sm-start {
    float: left !important;
  }
  .float-sm-end {
    float: right !important;
  }
  .float-sm-none {
    float: none !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-grid {
    display: grid !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: inline-flex !important;
  }
  .d-sm-none {
    display: none !important;
  }
  .flex-sm-fill {
    flex: 1 1 auto !important;
  }
  .flex-sm-row {
    flex-direction: row !important;
  }
  .flex-sm-column {
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-sm-0 {
    gap: 0 !important;
  }
  .gap-sm-1 {
    gap: 0.25rem !important;
  }
  .gap-sm-2 {
    gap: 0.5rem !important;
  }
  .gap-sm-3 {
    gap: 1rem !important;
  }
  .gap-sm-4 {
    gap: 1.5rem !important;
  }
  .gap-sm-5 {
    gap: 3rem !important;
  }
  .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    justify-content: center !important;
  }
  .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .justify-content-sm-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-sm-start {
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    align-items: center !important;
  }
  .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .align-content-sm-start {
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    align-content: center !important;
  }
  .align-content-sm-between {
    align-content: space-between !important;
  }
  .align-content-sm-around {
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    align-self: auto !important;
  }
  .align-self-sm-start {
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    align-self: center !important;
  }
  .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    align-self: stretch !important;
  }
  .order-sm-first {
    order: -1 !important;
  }
  .order-sm-0 {
    order: 0 !important;
  }
  .order-sm-1 {
    order: 1 !important;
  }
  .order-sm-2 {
    order: 2 !important;
  }
  .order-sm-3 {
    order: 3 !important;
  }
  .order-sm-4 {
    order: 4 !important;
  }
  .order-sm-5 {
    order: 5 !important;
  }
  .order-sm-last {
    order: 6 !important;
  }
  .m-sm-0 {
    margin: 0 !important;
  }
  .m-sm-1 {
    margin: 0.25rem !important;
  }
  .m-sm-2 {
    margin: 0.5rem !important;
  }
  .m-sm-3 {
    margin: 1rem !important;
  }
  .m-sm-4 {
    margin: 1.5rem !important;
  }
  .m-sm-5 {
    margin: 3rem !important;
  }
  .m-sm-auto {
    margin: auto !important;
  }
  .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-sm-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-sm-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-sm-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-sm-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-sm-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-sm-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-sm-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-sm-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-sm-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-sm-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-sm-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-sm-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-sm-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-sm-0 {
    margin-top: 0 !important;
  }
  .mt-sm-1 {
    margin-top: 0.25rem !important;
  }
  .mt-sm-2 {
    margin-top: 0.5rem !important;
  }
  .mt-sm-3 {
    margin-top: 1rem !important;
  }
  .mt-sm-4 {
    margin-top: 1.5rem !important;
  }
  .mt-sm-5 {
    margin-top: 3rem !important;
  }
  .mt-sm-auto {
    margin-top: auto !important;
  }
  .me-sm-0 {
    margin-right: 0 !important;
  }
  .me-sm-1 {
    margin-right: 0.25rem !important;
  }
  .me-sm-2 {
    margin-right: 0.5rem !important;
  }
  .me-sm-3 {
    margin-right: 1rem !important;
  }
  .me-sm-4 {
    margin-right: 1.5rem !important;
  }
  .me-sm-5 {
    margin-right: 3rem !important;
  }
  .me-sm-auto {
    margin-right: auto !important;
  }
  .mb-sm-0 {
    margin-bottom: 0 !important;
  }
  .mb-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-sm-3 {
    margin-bottom: 1rem !important;
  }
  .mb-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-sm-5 {
    margin-bottom: 3rem !important;
  }
  .mb-sm-auto {
    margin-bottom: auto !important;
  }
  .ms-sm-0 {
    margin-left: 0 !important;
  }
  .ms-sm-1 {
    margin-left: 0.25rem !important;
  }
  .ms-sm-2 {
    margin-left: 0.5rem !important;
  }
  .ms-sm-3 {
    margin-left: 1rem !important;
  }
  .ms-sm-4 {
    margin-left: 1.5rem !important;
  }
  .ms-sm-5 {
    margin-left: 3rem !important;
  }
  .ms-sm-auto {
    margin-left: auto !important;
  }
  .p-sm-0 {
    padding: 0 !important;
  }
  .p-sm-1 {
    padding: 0.25rem !important;
  }
  .p-sm-2 {
    padding: 0.5rem !important;
  }
  .p-sm-3 {
    padding: 1rem !important;
  }
  .p-sm-4 {
    padding: 1.5rem !important;
  }
  .p-sm-5 {
    padding: 3rem !important;
  }
  .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-sm-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-sm-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-sm-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-sm-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-sm-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-sm-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-sm-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-sm-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-sm-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-sm-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-sm-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-sm-0 {
    padding-top: 0 !important;
  }
  .pt-sm-1 {
    padding-top: 0.25rem !important;
  }
  .pt-sm-2 {
    padding-top: 0.5rem !important;
  }
  .pt-sm-3 {
    padding-top: 1rem !important;
  }
  .pt-sm-4 {
    padding-top: 1.5rem !important;
  }
  .pt-sm-5 {
    padding-top: 3rem !important;
  }
  .pe-sm-0 {
    padding-right: 0 !important;
  }
  .pe-sm-1 {
    padding-right: 0.25rem !important;
  }
  .pe-sm-2 {
    padding-right: 0.5rem !important;
  }
  .pe-sm-3 {
    padding-right: 1rem !important;
  }
  .pe-sm-4 {
    padding-right: 1.5rem !important;
  }
  .pe-sm-5 {
    padding-right: 3rem !important;
  }
  .pb-sm-0 {
    padding-bottom: 0 !important;
  }
  .pb-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-sm-3 {
    padding-bottom: 1rem !important;
  }
  .pb-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-sm-5 {
    padding-bottom: 3rem !important;
  }
  .ps-sm-0 {
    padding-left: 0 !important;
  }
  .ps-sm-1 {
    padding-left: 0.25rem !important;
  }
  .ps-sm-2 {
    padding-left: 0.5rem !important;
  }
  .ps-sm-3 {
    padding-left: 1rem !important;
  }
  .ps-sm-4 {
    padding-left: 1.5rem !important;
  }
  .ps-sm-5 {
    padding-left: 3rem !important;
  }
  .text-sm-start {
    text-align: left !important;
  }
  .text-sm-end {
    text-align: right !important;
  }
  .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  .float-md-start {
    float: left !important;
  }
  .float-md-end {
    float: right !important;
  }
  .float-md-none {
    float: none !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-grid {
    display: grid !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: flex !important;
  }
  .d-md-inline-flex {
    display: inline-flex !important;
  }
  .d-md-none {
    display: none !important;
  }
  .flex-md-fill {
    flex: 1 1 auto !important;
  }
  .flex-md-row {
    flex-direction: row !important;
  }
  .flex-md-column {
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-md-0 {
    gap: 0 !important;
  }
  .gap-md-1 {
    gap: 0.25rem !important;
  }
  .gap-md-2 {
    gap: 0.5rem !important;
  }
  .gap-md-3 {
    gap: 1rem !important;
  }
  .gap-md-4 {
    gap: 1.5rem !important;
  }
  .gap-md-5 {
    gap: 3rem !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .justify-content-md-between {
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    justify-content: space-around !important;
  }
  .justify-content-md-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-md-start {
    align-items: flex-start !important;
  }
  .align-items-md-end {
    align-items: flex-end !important;
  }
  .align-items-md-center {
    align-items: center !important;
  }
  .align-items-md-baseline {
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    align-items: stretch !important;
  }
  .align-content-md-start {
    align-content: flex-start !important;
  }
  .align-content-md-end {
    align-content: flex-end !important;
  }
  .align-content-md-center {
    align-content: center !important;
  }
  .align-content-md-between {
    align-content: space-between !important;
  }
  .align-content-md-around {
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    align-content: stretch !important;
  }
  .align-self-md-auto {
    align-self: auto !important;
  }
  .align-self-md-start {
    align-self: flex-start !important;
  }
  .align-self-md-end {
    align-self: flex-end !important;
  }
  .align-self-md-center {
    align-self: center !important;
  }
  .align-self-md-baseline {
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    align-self: stretch !important;
  }
  .order-md-first {
    order: -1 !important;
  }
  .order-md-0 {
    order: 0 !important;
  }
  .order-md-1 {
    order: 1 !important;
  }
  .order-md-2 {
    order: 2 !important;
  }
  .order-md-3 {
    order: 3 !important;
  }
  .order-md-4 {
    order: 4 !important;
  }
  .order-md-5 {
    order: 5 !important;
  }
  .order-md-last {
    order: 6 !important;
  }
  .m-md-0 {
    margin: 0 !important;
  }
  .m-md-1 {
    margin: 0.25rem !important;
  }
  .m-md-2 {
    margin: 0.5rem !important;
  }
  .m-md-3 {
    margin: 1rem !important;
  }
  .m-md-4 {
    margin: 1.5rem !important;
  }
  .m-md-5 {
    margin: 3rem !important;
  }
  .m-md-auto {
    margin: auto !important;
  }
  .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-md-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-md-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-md-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-md-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-md-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-md-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-md-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-md-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-md-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-md-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-md-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-md-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-md-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-md-0 {
    margin-top: 0 !important;
  }
  .mt-md-1 {
    margin-top: 0.25rem !important;
  }
  .mt-md-2 {
    margin-top: 0.5rem !important;
  }
  .mt-md-3 {
    margin-top: 1rem !important;
  }
  .mt-md-4 {
    margin-top: 1.5rem !important;
  }
  .mt-md-5 {
    margin-top: 3rem !important;
  }
  .mt-md-auto {
    margin-top: auto !important;
  }
  .me-md-0 {
    margin-right: 0 !important;
  }
  .me-md-1 {
    margin-right: 0.25rem !important;
  }
  .me-md-2 {
    margin-right: 0.5rem !important;
  }
  .me-md-3 {
    margin-right: 1rem !important;
  }
  .me-md-4 {
    margin-right: 1.5rem !important;
  }
  .me-md-5 {
    margin-right: 3rem !important;
  }
  .me-md-auto {
    margin-right: auto !important;
  }
  .mb-md-0 {
    margin-bottom: 0 !important;
  }
  .mb-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-md-3 {
    margin-bottom: 1rem !important;
  }
  .mb-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-md-5 {
    margin-bottom: 3rem !important;
  }
  .mb-md-auto {
    margin-bottom: auto !important;
  }
  .ms-md-0 {
    margin-left: 0 !important;
  }
  .ms-md-1 {
    margin-left: 0.25rem !important;
  }
  .ms-md-2 {
    margin-left: 0.5rem !important;
  }
  .ms-md-3 {
    margin-left: 1rem !important;
  }
  .ms-md-4 {
    margin-left: 1.5rem !important;
  }
  .ms-md-5 {
    margin-left: 3rem !important;
  }
  .ms-md-auto {
    margin-left: auto !important;
  }
  .p-md-0 {
    padding: 0 !important;
  }
  .p-md-1 {
    padding: 0.25rem !important;
  }
  .p-md-2 {
    padding: 0.5rem !important;
  }
  .p-md-3 {
    padding: 1rem !important;
  }
  .p-md-4 {
    padding: 1.5rem !important;
  }
  .p-md-5 {
    padding: 3rem !important;
  }
  .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-md-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-md-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-md-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-md-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-md-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-md-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-md-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-md-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-md-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-md-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-md-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-md-0 {
    padding-top: 0 !important;
  }
  .pt-md-1 {
    padding-top: 0.25rem !important;
  }
  .pt-md-2 {
    padding-top: 0.5rem !important;
  }
  .pt-md-3 {
    padding-top: 1rem !important;
  }
  .pt-md-4 {
    padding-top: 1.5rem !important;
  }
  .pt-md-5 {
    padding-top: 3rem !important;
  }
  .pe-md-0 {
    padding-right: 0 !important;
  }
  .pe-md-1 {
    padding-right: 0.25rem !important;
  }
  .pe-md-2 {
    padding-right: 0.5rem !important;
  }
  .pe-md-3 {
    padding-right: 1rem !important;
  }
  .pe-md-4 {
    padding-right: 1.5rem !important;
  }
  .pe-md-5 {
    padding-right: 3rem !important;
  }
  .pb-md-0 {
    padding-bottom: 0 !important;
  }
  .pb-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-md-3 {
    padding-bottom: 1rem !important;
  }
  .pb-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-md-5 {
    padding-bottom: 3rem !important;
  }
  .ps-md-0 {
    padding-left: 0 !important;
  }
  .ps-md-1 {
    padding-left: 0.25rem !important;
  }
  .ps-md-2 {
    padding-left: 0.5rem !important;
  }
  .ps-md-3 {
    padding-left: 1rem !important;
  }
  .ps-md-4 {
    padding-left: 1.5rem !important;
  }
  .ps-md-5 {
    padding-left: 3rem !important;
  }
  .text-md-start {
    text-align: left !important;
  }
  .text-md-end {
    text-align: right !important;
  }
  .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 992px) {
  .float-lg-start {
    float: left !important;
  }
  .float-lg-end {
    float: right !important;
  }
  .float-lg-none {
    float: none !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-grid {
    display: grid !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: inline-flex !important;
  }
  .d-lg-none {
    display: none !important;
  }
  .flex-lg-fill {
    flex: 1 1 auto !important;
  }
  .flex-lg-row {
    flex-direction: row !important;
  }
  .flex-lg-column {
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-lg-0 {
    gap: 0 !important;
  }
  .gap-lg-1 {
    gap: 0.25rem !important;
  }
  .gap-lg-2 {
    gap: 0.5rem !important;
  }
  .gap-lg-3 {
    gap: 1rem !important;
  }
  .gap-lg-4 {
    gap: 1.5rem !important;
  }
  .gap-lg-5 {
    gap: 3rem !important;
  }
  .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    justify-content: center !important;
  }
  .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .justify-content-lg-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-lg-start {
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    align-items: center !important;
  }
  .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .align-content-lg-start {
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    align-content: center !important;
  }
  .align-content-lg-between {
    align-content: space-between !important;
  }
  .align-content-lg-around {
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    align-self: auto !important;
  }
  .align-self-lg-start {
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    align-self: center !important;
  }
  .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    align-self: stretch !important;
  }
  .order-lg-first {
    order: -1 !important;
  }
  .order-lg-0 {
    order: 0 !important;
  }
  .order-lg-1 {
    order: 1 !important;
  }
  .order-lg-2 {
    order: 2 !important;
  }
  .order-lg-3 {
    order: 3 !important;
  }
  .order-lg-4 {
    order: 4 !important;
  }
  .order-lg-5 {
    order: 5 !important;
  }
  .order-lg-last {
    order: 6 !important;
  }
  .m-lg-0 {
    margin: 0 !important;
  }
  .m-lg-1 {
    margin: 0.25rem !important;
  }
  .m-lg-2 {
    margin: 0.5rem !important;
  }
  .m-lg-3 {
    margin: 1rem !important;
  }
  .m-lg-4 {
    margin: 1.5rem !important;
  }
  .m-lg-5 {
    margin: 3rem !important;
  }
  .m-lg-auto {
    margin: auto !important;
  }
  .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-lg-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-lg-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-lg-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-lg-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-lg-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-lg-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-lg-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-lg-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-lg-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-lg-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-lg-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-lg-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-lg-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-lg-0 {
    margin-top: 0 !important;
  }
  .mt-lg-1 {
    margin-top: 0.25rem !important;
  }
  .mt-lg-2 {
    margin-top: 0.5rem !important;
  }
  .mt-lg-3 {
    margin-top: 1rem !important;
  }
  .mt-lg-4 {
    margin-top: 1.5rem !important;
  }
  .mt-lg-5 {
    margin-top: 3rem !important;
  }
  .mt-lg-auto {
    margin-top: auto !important;
  }
  .me-lg-0 {
    margin-right: 0 !important;
  }
  .me-lg-1 {
    margin-right: 0.25rem !important;
  }
  .me-lg-2 {
    margin-right: 0.5rem !important;
  }
  .me-lg-3 {
    margin-right: 1rem !important;
  }
  .me-lg-4 {
    margin-right: 1.5rem !important;
  }
  .me-lg-5 {
    margin-right: 3rem !important;
  }
  .me-lg-auto {
    margin-right: auto !important;
  }
  .mb-lg-0 {
    margin-bottom: 0 !important;
  }
  .mb-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-lg-3 {
    margin-bottom: 1rem !important;
  }
  .mb-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-lg-5 {
    margin-bottom: 3rem !important;
  }
  .mb-lg-auto {
    margin-bottom: auto !important;
  }
  .ms-lg-0 {
    margin-left: 0 !important;
  }
  .ms-lg-1 {
    margin-left: 0.25rem !important;
  }
  .ms-lg-2 {
    margin-left: 0.5rem !important;
  }
  .ms-lg-3 {
    margin-left: 1rem !important;
  }
  .ms-lg-4 {
    margin-left: 1.5rem !important;
  }
  .ms-lg-5 {
    margin-left: 3rem !important;
  }
  .ms-lg-auto {
    margin-left: auto !important;
  }
  .p-lg-0 {
    padding: 0 !important;
  }
  .p-lg-1 {
    padding: 0.25rem !important;
  }
  .p-lg-2 {
    padding: 0.5rem !important;
  }
  .p-lg-3 {
    padding: 1rem !important;
  }
  .p-lg-4 {
    padding: 1.5rem !important;
  }
  .p-lg-5 {
    padding: 3rem !important;
  }
  .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-lg-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-lg-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-lg-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-lg-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-lg-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-lg-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-lg-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-lg-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-lg-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-lg-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-lg-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-lg-0 {
    padding-top: 0 !important;
  }
  .pt-lg-1 {
    padding-top: 0.25rem !important;
  }
  .pt-lg-2 {
    padding-top: 0.5rem !important;
  }
  .pt-lg-3 {
    padding-top: 1rem !important;
  }
  .pt-lg-4 {
    padding-top: 1.5rem !important;
  }
  .pt-lg-5 {
    padding-top: 3rem !important;
  }
  .pe-lg-0 {
    padding-right: 0 !important;
  }
  .pe-lg-1 {
    padding-right: 0.25rem !important;
  }
  .pe-lg-2 {
    padding-right: 0.5rem !important;
  }
  .pe-lg-3 {
    padding-right: 1rem !important;
  }
  .pe-lg-4 {
    padding-right: 1.5rem !important;
  }
  .pe-lg-5 {
    padding-right: 3rem !important;
  }
  .pb-lg-0 {
    padding-bottom: 0 !important;
  }
  .pb-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-lg-3 {
    padding-bottom: 1rem !important;
  }
  .pb-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-lg-5 {
    padding-bottom: 3rem !important;
  }
  .ps-lg-0 {
    padding-left: 0 !important;
  }
  .ps-lg-1 {
    padding-left: 0.25rem !important;
  }
  .ps-lg-2 {
    padding-left: 0.5rem !important;
  }
  .ps-lg-3 {
    padding-left: 1rem !important;
  }
  .ps-lg-4 {
    padding-left: 1.5rem !important;
  }
  .ps-lg-5 {
    padding-left: 3rem !important;
  }
  .text-lg-start {
    text-align: left !important;
  }
  .text-lg-end {
    text-align: right !important;
  }
  .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .float-xl-start {
    float: left !important;
  }
  .float-xl-end {
    float: right !important;
  }
  .float-xl-none {
    float: none !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-grid {
    display: grid !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: inline-flex !important;
  }
  .d-xl-none {
    display: none !important;
  }
  .flex-xl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xl-row {
    flex-direction: row !important;
  }
  .flex-xl-column {
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-xl-0 {
    gap: 0 !important;
  }
  .gap-xl-1 {
    gap: 0.25rem !important;
  }
  .gap-xl-2 {
    gap: 0.5rem !important;
  }
  .gap-xl-3 {
    gap: 1rem !important;
  }
  .gap-xl-4 {
    gap: 1.5rem !important;
  }
  .gap-xl-5 {
    gap: 3rem !important;
  }
  .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    justify-content: center !important;
  }
  .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .justify-content-xl-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-xl-start {
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    align-items: center !important;
  }
  .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .align-content-xl-start {
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    align-content: center !important;
  }
  .align-content-xl-between {
    align-content: space-between !important;
  }
  .align-content-xl-around {
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    align-self: auto !important;
  }
  .align-self-xl-start {
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    align-self: center !important;
  }
  .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    align-self: stretch !important;
  }
  .order-xl-first {
    order: -1 !important;
  }
  .order-xl-0 {
    order: 0 !important;
  }
  .order-xl-1 {
    order: 1 !important;
  }
  .order-xl-2 {
    order: 2 !important;
  }
  .order-xl-3 {
    order: 3 !important;
  }
  .order-xl-4 {
    order: 4 !important;
  }
  .order-xl-5 {
    order: 5 !important;
  }
  .order-xl-last {
    order: 6 !important;
  }
  .m-xl-0 {
    margin: 0 !important;
  }
  .m-xl-1 {
    margin: 0.25rem !important;
  }
  .m-xl-2 {
    margin: 0.5rem !important;
  }
  .m-xl-3 {
    margin: 1rem !important;
  }
  .m-xl-4 {
    margin: 1.5rem !important;
  }
  .m-xl-5 {
    margin: 3rem !important;
  }
  .m-xl-auto {
    margin: auto !important;
  }
  .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-xl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-xl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-xl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-xl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-xl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-xl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-xl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-xl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-xl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-xl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-xl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-xl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-xl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-xl-0 {
    margin-top: 0 !important;
  }
  .mt-xl-1 {
    margin-top: 0.25rem !important;
  }
  .mt-xl-2 {
    margin-top: 0.5rem !important;
  }
  .mt-xl-3 {
    margin-top: 1rem !important;
  }
  .mt-xl-4 {
    margin-top: 1.5rem !important;
  }
  .mt-xl-5 {
    margin-top: 3rem !important;
  }
  .mt-xl-auto {
    margin-top: auto !important;
  }
  .me-xl-0 {
    margin-right: 0 !important;
  }
  .me-xl-1 {
    margin-right: 0.25rem !important;
  }
  .me-xl-2 {
    margin-right: 0.5rem !important;
  }
  .me-xl-3 {
    margin-right: 1rem !important;
  }
  .me-xl-4 {
    margin-right: 1.5rem !important;
  }
  .me-xl-5 {
    margin-right: 3rem !important;
  }
  .me-xl-auto {
    margin-right: auto !important;
  }
  .mb-xl-0 {
    margin-bottom: 0 !important;
  }
  .mb-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-xl-3 {
    margin-bottom: 1rem !important;
  }
  .mb-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-xl-5 {
    margin-bottom: 3rem !important;
  }
  .mb-xl-auto {
    margin-bottom: auto !important;
  }
  .ms-xl-0 {
    margin-left: 0 !important;
  }
  .ms-xl-1 {
    margin-left: 0.25rem !important;
  }
  .ms-xl-2 {
    margin-left: 0.5rem !important;
  }
  .ms-xl-3 {
    margin-left: 1rem !important;
  }
  .ms-xl-4 {
    margin-left: 1.5rem !important;
  }
  .ms-xl-5 {
    margin-left: 3rem !important;
  }
  .ms-xl-auto {
    margin-left: auto !important;
  }
  .p-xl-0 {
    padding: 0 !important;
  }
  .p-xl-1 {
    padding: 0.25rem !important;
  }
  .p-xl-2 {
    padding: 0.5rem !important;
  }
  .p-xl-3 {
    padding: 1rem !important;
  }
  .p-xl-4 {
    padding: 1.5rem !important;
  }
  .p-xl-5 {
    padding: 3rem !important;
  }
  .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-xl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-xl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-xl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-xl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-xl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-xl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-xl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-xl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-xl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-xl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-xl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-xl-0 {
    padding-top: 0 !important;
  }
  .pt-xl-1 {
    padding-top: 0.25rem !important;
  }
  .pt-xl-2 {
    padding-top: 0.5rem !important;
  }
  .pt-xl-3 {
    padding-top: 1rem !important;
  }
  .pt-xl-4 {
    padding-top: 1.5rem !important;
  }
  .pt-xl-5 {
    padding-top: 3rem !important;
  }
  .pe-xl-0 {
    padding-right: 0 !important;
  }
  .pe-xl-1 {
    padding-right: 0.25rem !important;
  }
  .pe-xl-2 {
    padding-right: 0.5rem !important;
  }
  .pe-xl-3 {
    padding-right: 1rem !important;
  }
  .pe-xl-4 {
    padding-right: 1.5rem !important;
  }
  .pe-xl-5 {
    padding-right: 3rem !important;
  }
  .pb-xl-0 {
    padding-bottom: 0 !important;
  }
  .pb-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-xl-3 {
    padding-bottom: 1rem !important;
  }
  .pb-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-xl-5 {
    padding-bottom: 3rem !important;
  }
  .ps-xl-0 {
    padding-left: 0 !important;
  }
  .ps-xl-1 {
    padding-left: 0.25rem !important;
  }
  .ps-xl-2 {
    padding-left: 0.5rem !important;
  }
  .ps-xl-3 {
    padding-left: 1rem !important;
  }
  .ps-xl-4 {
    padding-left: 1.5rem !important;
  }
  .ps-xl-5 {
    padding-left: 3rem !important;
  }
  .text-xl-start {
    text-align: left !important;
  }
  .text-xl-end {
    text-align: right !important;
  }
  .text-xl-center {
    text-align: center !important;
  }
}
@media (min-width: 1400px) {
  .float-xxl-start {
    float: left !important;
  }
  .float-xxl-end {
    float: right !important;
  }
  .float-xxl-none {
    float: none !important;
  }
  .d-xxl-inline {
    display: inline !important;
  }
  .d-xxl-inline-block {
    display: inline-block !important;
  }
  .d-xxl-block {
    display: block !important;
  }
  .d-xxl-grid {
    display: grid !important;
  }
  .d-xxl-table {
    display: table !important;
  }
  .d-xxl-table-row {
    display: table-row !important;
  }
  .d-xxl-table-cell {
    display: table-cell !important;
  }
  .d-xxl-flex {
    display: flex !important;
  }
  .d-xxl-inline-flex {
    display: inline-flex !important;
  }
  .d-xxl-none {
    display: none !important;
  }
  .flex-xxl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xxl-row {
    flex-direction: row !important;
  }
  .flex-xxl-column {
    flex-direction: column !important;
  }
  .flex-xxl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xxl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xxl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xxl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xxl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xxl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-xxl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xxl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xxl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-xxl-0 {
    gap: 0 !important;
  }
  .gap-xxl-1 {
    gap: 0.25rem !important;
  }
  .gap-xxl-2 {
    gap: 0.5rem !important;
  }
  .gap-xxl-3 {
    gap: 1rem !important;
  }
  .gap-xxl-4 {
    gap: 1.5rem !important;
  }
  .gap-xxl-5 {
    gap: 3rem !important;
  }
  .justify-content-xxl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xxl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xxl-center {
    justify-content: center !important;
  }
  .justify-content-xxl-between {
    justify-content: space-between !important;
  }
  .justify-content-xxl-around {
    justify-content: space-around !important;
  }
  .justify-content-xxl-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-xxl-start {
    align-items: flex-start !important;
  }
  .align-items-xxl-end {
    align-items: flex-end !important;
  }
  .align-items-xxl-center {
    align-items: center !important;
  }
  .align-items-xxl-baseline {
    align-items: baseline !important;
  }
  .align-items-xxl-stretch {
    align-items: stretch !important;
  }
  .align-content-xxl-start {
    align-content: flex-start !important;
  }
  .align-content-xxl-end {
    align-content: flex-end !important;
  }
  .align-content-xxl-center {
    align-content: center !important;
  }
  .align-content-xxl-between {
    align-content: space-between !important;
  }
  .align-content-xxl-around {
    align-content: space-around !important;
  }
  .align-content-xxl-stretch {
    align-content: stretch !important;
  }
  .align-self-xxl-auto {
    align-self: auto !important;
  }
  .align-self-xxl-start {
    align-self: flex-start !important;
  }
  .align-self-xxl-end {
    align-self: flex-end !important;
  }
  .align-self-xxl-center {
    align-self: center !important;
  }
  .align-self-xxl-baseline {
    align-self: baseline !important;
  }
  .align-self-xxl-stretch {
    align-self: stretch !important;
  }
  .order-xxl-first {
    order: -1 !important;
  }
  .order-xxl-0 {
    order: 0 !important;
  }
  .order-xxl-1 {
    order: 1 !important;
  }
  .order-xxl-2 {
    order: 2 !important;
  }
  .order-xxl-3 {
    order: 3 !important;
  }
  .order-xxl-4 {
    order: 4 !important;
  }
  .order-xxl-5 {
    order: 5 !important;
  }
  .order-xxl-last {
    order: 6 !important;
  }
  .m-xxl-0 {
    margin: 0 !important;
  }
  .m-xxl-1 {
    margin: 0.25rem !important;
  }
  .m-xxl-2 {
    margin: 0.5rem !important;
  }
  .m-xxl-3 {
    margin: 1rem !important;
  }
  .m-xxl-4 {
    margin: 1.5rem !important;
  }
  .m-xxl-5 {
    margin: 3rem !important;
  }
  .m-xxl-auto {
    margin: auto !important;
  }
  .mx-xxl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-xxl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-xxl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-xxl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-xxl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-xxl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-xxl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-xxl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-xxl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-xxl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-xxl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-xxl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-xxl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-xxl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-xxl-0 {
    margin-top: 0 !important;
  }
  .mt-xxl-1 {
    margin-top: 0.25rem !important;
  }
  .mt-xxl-2 {
    margin-top: 0.5rem !important;
  }
  .mt-xxl-3 {
    margin-top: 1rem !important;
  }
  .mt-xxl-4 {
    margin-top: 1.5rem !important;
  }
  .mt-xxl-5 {
    margin-top: 3rem !important;
  }
  .mt-xxl-auto {
    margin-top: auto !important;
  }
  .me-xxl-0 {
    margin-right: 0 !important;
  }
  .me-xxl-1 {
    margin-right: 0.25rem !important;
  }
  .me-xxl-2 {
    margin-right: 0.5rem !important;
  }
  .me-xxl-3 {
    margin-right: 1rem !important;
  }
  .me-xxl-4 {
    margin-right: 1.5rem !important;
  }
  .me-xxl-5 {
    margin-right: 3rem !important;
  }
  .me-xxl-auto {
    margin-right: auto !important;
  }
  .mb-xxl-0 {
    margin-bottom: 0 !important;
  }
  .mb-xxl-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-xxl-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-xxl-3 {
    margin-bottom: 1rem !important;
  }
  .mb-xxl-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-xxl-5 {
    margin-bottom: 3rem !important;
  }
  .mb-xxl-auto {
    margin-bottom: auto !important;
  }
  .ms-xxl-0 {
    margin-left: 0 !important;
  }
  .ms-xxl-1 {
    margin-left: 0.25rem !important;
  }
  .ms-xxl-2 {
    margin-left: 0.5rem !important;
  }
  .ms-xxl-3 {
    margin-left: 1rem !important;
  }
  .ms-xxl-4 {
    margin-left: 1.5rem !important;
  }
  .ms-xxl-5 {
    margin-left: 3rem !important;
  }
  .ms-xxl-auto {
    margin-left: auto !important;
  }
  .p-xxl-0 {
    padding: 0 !important;
  }
  .p-xxl-1 {
    padding: 0.25rem !important;
  }
  .p-xxl-2 {
    padding: 0.5rem !important;
  }
  .p-xxl-3 {
    padding: 1rem !important;
  }
  .p-xxl-4 {
    padding: 1.5rem !important;
  }
  .p-xxl-5 {
    padding: 3rem !important;
  }
  .px-xxl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-xxl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-xxl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-xxl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-xxl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-xxl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-xxl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-xxl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-xxl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-xxl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-xxl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-xxl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-xxl-0 {
    padding-top: 0 !important;
  }
  .pt-xxl-1 {
    padding-top: 0.25rem !important;
  }
  .pt-xxl-2 {
    padding-top: 0.5rem !important;
  }
  .pt-xxl-3 {
    padding-top: 1rem !important;
  }
  .pt-xxl-4 {
    padding-top: 1.5rem !important;
  }
  .pt-xxl-5 {
    padding-top: 3rem !important;
  }
  .pe-xxl-0 {
    padding-right: 0 !important;
  }
  .pe-xxl-1 {
    padding-right: 0.25rem !important;
  }
  .pe-xxl-2 {
    padding-right: 0.5rem !important;
  }
  .pe-xxl-3 {
    padding-right: 1rem !important;
  }
  .pe-xxl-4 {
    padding-right: 1.5rem !important;
  }
  .pe-xxl-5 {
    padding-right: 3rem !important;
  }
  .pb-xxl-0 {
    padding-bottom: 0 !important;
  }
  .pb-xxl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-xxl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-xxl-3 {
    padding-bottom: 1rem !important;
  }
  .pb-xxl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-xxl-5 {
    padding-bottom: 3rem !important;
  }
  .ps-xxl-0 {
    padding-left: 0 !important;
  }
  .ps-xxl-1 {
    padding-left: 0.25rem !important;
  }
  .ps-xxl-2 {
    padding-left: 0.5rem !important;
  }
  .ps-xxl-3 {
    padding-left: 1rem !important;
  }
  .ps-xxl-4 {
    padding-left: 1.5rem !important;
  }
  .ps-xxl-5 {
    padding-left: 3rem !important;
  }
  .text-xxl-start {
    text-align: left !important;
  }
  .text-xxl-end {
    text-align: right !important;
  }
  .text-xxl-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .fs-1 {
    font-size: 2.5rem !important;
  }
  .fs-2 {
    font-size: 2rem !important;
  }
  .fs-3 {
    font-size: 1.75rem !important;
  }
  .fs-4 {
    font-size: 1.5rem !important;
  }
}
@media print {
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-grid {
    display: grid !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: flex !important;
  }
  .d-print-inline-flex {
    display: inline-flex !important;
  }
  .d-print-none {
    display: none !important;
  }
}
/**
 * Set up a decent box model on the root element
 */
html {
  box-sizing: border-box;
}

/**
 * Make all elements from the DOM inherit from the parent box-sizing
 * Since `*` has a specificity of 0, it does not override the `html` value
 * making all elements inheriting from the root box-sizing value
 * See: https://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/
 */
*,
*::before,
*::after {
  box-sizing: inherit;
}

body {
  background-color: hsl(0, 0%, 97.5%);
}

/**
 * Basic styles for links
 */
a {
  color: hsl(0, 0%, 13%);
  text-decoration: none;
  transition: all 0.4s ease-in-out;
}
a:focus-visible {
  outline: rgba(255, 255, 255, 0.7) dashed 1px;
}
a:hover, a:active, a:focus, a:focus-within {
  color: hsl(0, 0%, 11%);
  text-decoration: underline;
  transition: all 0.4s ease-in-out;
}

a.oh-link__unstyled {
  text-decoration: none;
}
a.oh-link__unstyled:hover {
  text-decoration: none;
}

#mainNav {
  padding-bottom: 60px;
}

.custom-dropdown {
  display: none;
  position: absolute;
  left: 15px;
  padding: 5px;
  z-index: 99;
  width: 400px;
  background-color: hsl(0, 0%, 100%);
  border-radius: 0rem;
  color: hsl(0, 0%, 11%);
  border: 1px solid hsl(0, 0%, 95%);
  box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
}

.custom-dropdown .search_content {
  margin: 0;
  padding: 0;
}

.custom-dropdown .search_content li {
  list-style: none;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #ddd;
  padding: 10px;
  word-break: break-word;
}

.custom-dropdown .search_content li:last-child {
  border-bottom: unset;
}

* {
  scrollbar-width: thin;
}

*::-moz-scrollbar-thumb {
  border-radius: 5px;
}

.search_content li a .search_text {
  color: hsl(8, 77%, 56%);
}

/* cyrillic-ext */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2JL7W0Q5n-wU.woff2) format("woff2");
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: "Inter";
  font-style: noarmal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa0ZL7W0Q5n-wU.woff2) format("woff2");
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2ZL7W0Q5n-wU.woff2) format("woff2");
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1pL7W0Q5n-wU.woff2) format("woff2");
  unicode-range: U+0370-03FF;
}
/* vietnamese */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2pL7W0Q5n-wU.woff2) format("woff2");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa25L7W0Q5n-wU.woff2) format("woff2");
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1ZL7W0Q5nw.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2JL7W0Q5n-wU.woff2) format("woff2");
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa0ZL7W0Q5n-wU.woff2) format("woff2");
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2ZL7W0Q5n-wU.woff2) format("woff2");
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1pL7W0Q5n-wU.woff2) format("woff2");
  unicode-range: U+0370-03FF;
}
/* vietnamese */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2pL7W0Q5n-wU.woff2) format("woff2");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa25L7W0Q5n-wU.woff2) format("woff2");
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1ZL7W0Q5nw.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* POPPINS */
/* devanagari */
@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v20/pxiEyp8kv8JHgFVrJJbecnFHGPezSQ.woff2) format("woff2");
  unicode-range: U+0900-097F, U+1CD0-1CF6, U+1CF8-1CF9, U+200C-200D, U+20A8, U+20B9, U+25CC, U+A830-A839, U+A8E0-A8FB;
}
/* latin-ext */
@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v20/pxiEyp8kv8JHgFVrJJnecnFHGPezSQ.woff2) format("woff2");
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v20/pxiEyp8kv8JHgFVrJJfecnFHGPc.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* devanagari */
@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v20/pxiByp8kv8JHgFVrLEj6Z11lFd2JQEl8qw.woff2) format("woff2");
  unicode-range: U+0900-097F, U+1CD0-1CF6, U+1CF8-1CF9, U+200C-200D, U+20A8, U+20B9, U+25CC, U+A830-A839, U+A8E0-A8FB;
}
/* latin-ext */
@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v20/pxiByp8kv8JHgFVrLEj6Z1JlFd2JQEl8qw.woff2) format("woff2");
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Poppins";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v20/pxiByp8kv8JHgFVrLEj6Z1xlFd2JQEk.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/**
 * Basic typography style for copy text
 */
body {
  color: hsl(0, 0%, 11%);
  font: normal 80%/1.4 "Poppins", "Open Sans", "Helvetica Neue Light", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/**
 * Clear inner floats
 */
.clearfix::after {
  clear: both;
  content: "";
  display: table;
}

/**
 * Main content containers
 * 1. Make the container full-width with a maximum width
 * 2. Center it in the viewport
 * 3. Leave some space on the edges, especially valuable on small screens
 */
.container {
  max-width: 1180px; /* 1 */
  margin-left: auto; /* 2 */
  margin-right: auto; /* 2 */
  padding-left: 20px; /* 3 */
  padding-right: 20px; /* 3 */
  width: 100%; /* 1 */
}

/**
 * Hide text while making it readable for screen readers
 * 1. Needed in WebKit-based browsers because of an implementation bug;
 *    See: https://code.google.com/p/chromium/issues/detail?id=457146
 */
.hide-text {
  overflow: hidden;
  padding: 0; /* 1 */
  text-indent: 101%;
  white-space: nowrap;
}

/**
 * Hide element while making it readable for screen readers
 * Shamelessly borrowed from HTML5Boilerplate:
 * https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css#L119-L133
 */
.visually-hidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

/**
* Margins
*/
.ml-2 {
  margin-left: 0.7rem;
}

.mr-2 {
  margin-right: 0.7rem;
}

.mr-1 {
  margin-right: 0.3rem;
}

.ml-1 {
  margin-left: 0.3rem;
}

.mt-4 {
  margin-top: 2rem;
}

.max-h-0 {
  max-height: 0;
}

@media (max-width: 767.98px) {
  .oh-mb-3--small {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 767.98px) {
  .d-md-flex-wrap {
    flex-wrap: wrap;
  }
}

@media (max-width: 767.98px) {
  .oh-d-flex-column--resp {
    display: flex;
    flex-direction: column;
    align-items: flex-start !important;
  }
}

.oh-navbar__toggle-link {
  display: flex;
  align-items: center;
}

.oh-navbar__toggle-menu {
  margin-right: 0.35rem;
}

.oh-navbar__page-title {
  font-size: 1.15rem;
}
@media (max-width: 1199.98px) {
  .oh-navbar__page-title {
    display: none;
  }
}
@media (max-width: 767.98px) {
  .oh-navbar__page-title {
    font-size: 0.98rem;
  }
}

.oh-navbar__toggle-link:hover {
  opacity: 0.8;
}

.oh-navbar {
  z-index: 999;
  height: 65px;
  width: -webkit-fill-available;
  width: -moz-available;
  width: -webkit-fill-available;
  width: fill-available;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: hsl(0, 0%, 100%);
  border-bottom: 3px solid hsl(0, 0%, 93%);
}

.oh-navbar__systray {
  display: flex;
  align-items: center;
  gap: 0;
  height: 100%;
}

.oh-navbar__wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.oh-navbar__user-info {
  display: flex;
  align-items: center;
  position: relative;
  padding: 0.5rem 1.5rem;
  border-radius: 0px;
  height: 100%;
  cursor: pointer;
}
@media (max-width: 575.98px) {
  .oh-navbar__user-info {
    padding: 0.5rem 0.75rem;
  }
}
.oh-navbar__user-info div:first-child {
  margin-right: 0.5rem;
}
.oh-navbar__user-info::after {
  content: "";
  position: relative;
  width: 14px;
  height: 14px;
  top: 2px;
  right: -5px;
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' fill='%231d1d1d' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m3 6.75 9 10.5 9-10.5H3Z'%3E%3C/path%3E%3C/svg%3E");
}
.oh-navbar__user-info:hover {
  background-color: hsl(0, 0%, 96%);
  transition: all 0.3s ease-in-out;
}

.oh-navbar__systray .oh-dropdown {
  height: 100%;
}

.oh-navbar__user-photo {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
}
@media (max-width: 575.98px) {
  .oh-navbar__user-photo {
    width: 24px;
    height: 24px;
  }
}

.oh-navbar__user-image {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

@media screen and (max-width: 1055.99px) {
  .oh-navbar__time {
    display: none !important;
  }
}

.oh-navbar__notification-link,
.oh-navbar__action-icons-link {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0px;
}

.oh-navbar__notification-beacon {
  min-width: 18px;
  min-height: 18px;
  border-radius: 30px;
  background-color: hsl(8, 77%, 56%);
  color: hsl(0, 0%, 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  position: absolute;
  top: 15px;
  right: 10px;
  width: -moz-fit-content;
  width: fit-content;
  padding: 0.1rem 0.4rem;
  height: -moz-fit-content;
  height: fit-content;
}

.oh-navbar__icon {
  width: 24px;
  height: 24px;
  color: hsl(216, 3%, 39%);
}
@media (max-width: 575.98px) {
  .oh-navbar__icon {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 991.98px) {
  .oh-navbar__user-name {
    display: none;
  }
}
/**
* Notifications
*/
.oh-navbar__notifications,
.oh-navbar__action-icons {
  position: relative;
  display: flex;
  height: 100%;
  padding: 0.4rem 1rem;
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
  cursor: pointer;
}
.oh-navbar__notifications:hover,
.oh-navbar__action-icons:hover {
  background-color: hsl(0, 0%, 96%);
}
@media (max-width: 575.98px) {
  .oh-navbar__notifications,
  .oh-navbar__action-icons {
    padding: 0.5rem;
    width: 50px;
    justify-content: center;
    border-left: none;
    border-right: none;
  }
  .oh-navbar__notifications:hover,
  .oh-navbar__action-icons:hover {
    background-color: transparent;
  }
}

.oh-navbar__notification-tray {
  position: absolute;
  width: 380px;
  padding-right: 15px;
  min-height: 250px;
  background-color: hsl(0, 0%, 100%);
  z-index: 99;
  top: 100%;
  right: 0px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
  border: 1px solid hsl(0, 0%, 95%);
}
@media (max-width: 575.98px) {
  .oh-navbar__notification-tray {
    position: fixed;
    left: 5px;
    width: 98%;
    top: 70px;
  }
}

.oh-navbar__notification-empty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 250px;
  padding: 35px;
}

.oh-navbar__notification-empty-title {
  display: block;
  font-weight: bold;
  color: hsl(216, 18%, 64%);
  margin-top: 0.75rem;
  margin-bottom: 0.3rem;
}

.oh-navbar__notification-empty-desc {
  color: hsl(216, 18%, 64%);
  font-size: 0.9rem;
}

.oh-navbar__notification-empty-title,
.oh-navbar__notification-empty-desc {
  text-align: center;
}

.oh-navbar__notification-head {
  width: 100%;
  padding: 1rem 1.15rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: hsl(0, 0%, 11%);
  border-bottom: 1px solid hsl(0, 0%, 95%);
}

.oh-navbar__notification-body {
  width: 100%;
  padding: 0 1.15rem;
  max-height: 400px;
  overflow: auto;
  display: block;
}

.oh-navbar__notification-footer {
  padding: 1.15rem;
  border-top: 1px solid hsl(0, 0%, 95%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.oh-navbar__notification-head-title {
  font-weight: bold;
  font-size: 0.9rem;
}

.oh-navbar__notification-links {
  display: flex;
  align-items: center;
}

.oh-navbar__notification-tray-link {
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  color: hsl(8, 77%, 56%);
}
.oh-navbar__notification-tray-link:hover, .oh-navbar__notification-tray-link:focus, .oh-navbar__notification-tray-link:focus-visible, .oh-navbar__notification-tray-link:active {
  color: hsl(8, 61%, 50%);
  text-decoration: none;
}

.oh-navbar__notification-tray-link--danger {
  color: hsl(1, 100%, 61%);
}
.oh-navbar__notification-tray-link--danger:hover, .oh-navbar__notification-tray-link--danger:focus, .oh-navbar__notification-tray-link--danger:focus-visible, .oh-navbar__notification-tray-link--danger:active {
  color: hsl(1, 100%, 61%);
  text-decoration: none;
}

.oh-navbar__nofication-list {
  list-style: none;
  padding-left: 0;
}

.oh-navbar__notification-dot {
  display: block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  position: relative;
  top: 5px;
  opacity: 0;
}

.oh-navbar__notification-dot--green {
  background-color: hsl(8, 77%, 56%);
}

.oh-navbar__notification-item {
  display: grid;
  grid-template-columns: 1fr 8fr 1fr;
  align-items: center;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1.3rem;
  border-bottom: 1px solid hsl(0, 0%, 95%);
}
.oh-navbar__notification-item:last-child {
  padding-bottom: 0px;
  border-bottom: none;
}

.oh-navbar__notification-item div:first-child {
  align-self: flex-start;
}
.oh-navbar__notification-item div:last-child {
  align-self: flex-start;
}

.oh-navbar__notification-image {
  background-color: hsl(8, 77%, 56%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: hsl(0, 0%, 100%);
  font-size: 1.15rem;
}

.oh-navbar__notification-text {
  margin-top: 0;
  margin-bottom: 0;
  color: hsl(0, 0%, 11%);
  font-size: 0.85rem;
}

.oh-navbar__notification-dot--unread {
  opacity: 1;
}

.oh-navbar__notification-text--unread {
  font-weight: bold;
}

.oh-navbar__notification-date {
  color: rgba(28.05, 28.05, 28.05, 0.6);
  font-size: 0.75rem;
}

.oh-navbar__clock {
  display: flex;
  align-items: center;
  padding: 0.5rem 1.5rem;
  height: 100%;
  transition: all 0 linear;
  border-left: 1px solid hsl(213, 22%, 93%);
}
.oh-navbar__clock:hover, .oh-navbar__clock:focus, .oh-navbar__clock:focus-visible {
  background-color: hsl(0, 0%, 95%);
  text-decoration: none;
  transition: all 0 linear;
}
@media (max-width: 575.98px) {
  .oh-navbar__clock {
    padding: 0.5rem;
    width: 50px;
    justify-content: center;
    border-left: none;
  }
  .oh-navbar__clock:hover, .oh-navbar__clock:focus, .oh-navbar__clock:focus-visible {
    background-color: transparent;
  }
}

.oh-navbar__clock-icon {
  color: hsl(216, 3%, 39%);
}
@media (max-width: 767.98px) {
  .oh-navbar__clock-icon {
    margin-right: 0rem !important;
  }
}

@media (max-width: 767.98px) {
  .oh-navbar__clock-text {
    display: none;
  }
  .oh-navbar__clock-icon {
    font-size: 1.5rem;
  }
}
@media (max-width: 575.98px) {
  .oh-navbar__clock-icon {
    font-size: 1.2rem;
  }
}
.oh-navbar__toggle-container {
  display: flex;
  align-items: center;
}

/**
* Breadcrumb
*/
.oh-navbar__breadcrumb {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  list-style: none;
  margin-bottom: 0;
  padding-left: 10px;
}
@media screen and (max-width: 991.98px) {
  .oh-navbar__breadcrumb {
    display: none;
  }
}

.oh-navbar__breadcrumb-item {
  margin-right: 25px;
  position: relative;
}
.oh-navbar__breadcrumb-item::after {
  content: "";
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDQiIGhlaWdodD0iNDQiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzczNzM3MyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBzdHJva2Utd2lkdGg9IjEuNSIgdmlld0JveD0iMCAwIDI0IDI0IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogIDxwYXRoIGQ9Im04LjYyNSA1LjI1IDYuNzUgNi43NS02Ljc1IDYuNzUiPjwvcGF0aD4KPC9zdmc+");
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  width: 16px;
  height: 16px;
  top: 3px;
  right: -20px;
}
.oh-navbar__breadcrumb-item:last-child {
  margin-right: 0;
}
.oh-navbar__breadcrumb-item:last-child::after {
  content: unset;
}

.oh-navbar__breadcrumb-link {
  color: hsl(0, 0%, 45%);
}
.oh-navbar__breadcrumb-link:hover {
  text-decoration: none;
}

.oh-navbar__breadcrumb-link.active {
  color: hsl(0, 0%, 11%);
}

#main {
  overflow-x: hidden;
}

.oh-wrapper-main {
  display: grid;
  grid-template-columns: 230px auto;
  transition: all 0.4s ease-in-out;
}

.oh-wrapper-main--no-grid {
  display: unset;
}

.oh-wrapper-main--closed {
  grid-template-columns: 90px auto;
}
@media (max-width: 767.98px) {
  .oh-wrapper-main--closed {
    grid-template-columns: auto;
  }
}

.oh-wrapper {
  max-width: 95%;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.oh-layout--grid-3 {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1%;
  row-gap: 1rem;
  justify-content: space-between;
}

/* Text */
.oh-text--sm {
  font-size: 0.9rem !important;
}

.oh-text--xs {
  font-size: 0.75rem !important;
}

/* Text Colors*/
.oh-text--dark {
  color: hsl(0, 0%, 11%);
}

.oh-text--light {
  color: hsl(0, 0%, 45%);
}

.oh-text--secondary {
  color: hsl(8, 77%, 56%);
}

/* Links */
.oh-link {
  display: flex;
  align-items: center;
}
.oh-link:hover, .oh-link:focus, .oh-link:focus-visible {
  text-decoration: none;
}

.oh-link--secondary {
  color: hsl(8, 77%, 56%);
}
.oh-link--secondary:hover, .oh-link--secondary:focus, .oh-link--secondary:focus-visible {
  color: hsl(8, 61%, 50%);
}

@media (max-width: 991.98px) {
  .oh-resp-hidden--lg {
    display: none;
  }
}

.oh-viewer {
  width: 80%;
  height: 900px;
  left: 50%;
  transform: translateX(-50%);
  position: relative;
}

.oh-viewer--image {
  width: 80%;
  height: auto;
  margin: 0 auto;
  border: 1px solid hsl(213, 22%, 93%);
}

.centered-div {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.oh-empty__title {
  font-size: 1.25rem;
  font-weight: bold;
  margin-top: 1rem;
}

.oh-empty__subtitle {
  color: hsl(0, 0%, 45%);
}

.oh-accordion-header {
  position: relative;
  padding: 1.25rem 0;
  border-bottom: 1px solid hsl(213, 22%, 93%);
  font-weight: bold;
  width: 100%;
  cursor: pointer;
}
.oh-accordion-header:hover {
  opacity: 0.8;
}
.oh-accordion-header::after {
  content: "";
  width: 25px;
  height: 25px;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9IiMxZjFmMWYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJtNyAxMCA1IDUgNS01SDdaIj48L3BhdGg+Cjwvc3ZnPg==");
  background-size: contain;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 0;
  top: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.oh-accordion:not(.oh-accordion--show):last-child .oh-accordion-header {
  border-bottom: none;
}

.oh-accordion--show .oh-accordion-header::after {
  content: "";
  width: 25px;
  height: 25px;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9IiMxZjFmMWYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJtNyAxNC41IDUtNSA1IDVIN1oiPjwvcGF0aD4KPC9zdmc+");
  background-size: contain;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 0;
  top: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.oh-accordion-body {
  display: none;
}

.oh-accordion--show .oh-accordion-body {
  display: block;
}

.oh-dropdown__filter-footer {
  border-top: 1px solid hsl(213, 22%, 93%);
  padding-top: 1rem;
}

/* ====================================
*          ACCORDION - META
* ================================== */
.oh-accordion-meta {
  width: 100%;
}

.oh-accordion-meta__header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid hsl(213, 22%, 93%);
  background-color: hsl(0, 0%, 100%);
  padding: 0.5rem 0.5rem 0.5rem 2.5rem;
  cursor: pointer;
}
.oh-accordion-meta__header:hover {
  background-color: rgba(233.223, 236.7573, 241.077, 0.4);
}
.oh-accordion-meta__header::before {
  content: "";
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzFjMWMxYyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBzdHJva2Utd2lkdGg9IjEuNSIgdmlld0JveD0iMCAwIDI0IDI0IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogIDxwYXRoIGQ9Ik0xMiA1LjI1djEzLjUiPjwvcGF0aD4KICA8cGF0aCBkPSJNMTguNzUgMTJINS4yNSI+PC9wYXRoPgo8L3N2Zz4=");
  background-position: center;
  background-repeat: no-repeat;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  -o-object-fit: contain;
     object-fit: contain;
  position: absolute;
  left: 8px;
}

.oh-accordion-meta__header.oh-accordion-meta__header--show::before {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzFjMWMxYyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBzdHJva2Utd2lkdGg9IjEuNSIgdmlld0JveD0iMCAwIDI0IDI0IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogIDxwYXRoIGQ9Ik0xOC43NSAxMkg1LjI1Ij48L3BhdGg+Cjwvc3ZnPg==");
}

.oh-accordion-meta__body {
  border: 1px solid hsl(213, 22%, 93%);
  border-top: none;
  padding: 0.5rem;
  background-color: hsl(0, 0%, 100%);
}

.oh-accordion-meta__item {
  margin-bottom: 0.5rem;
}
.oh-accordion-meta__item:last-child {
  margin-bottom: 0;
}

.oh-accordion-meta__btn, .oh-accordion-meta__btn-icon {
  color: hsl(0, 0%, 11%);
}

.centered-div {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.oh-user_permission-list_item {
  position: relative;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid hsl(213, 22%, 93%);
  background-color: hsl(0, 0%, 100%);
  padding: 0.5rem;
  margin-bottom: 0.5rem;
}
.oh-user_permission-list_item:hover {
  background-color: rgba(233.223, 236.7573, 241.077, 0.4);
}

.oh-user_permission-list_profile {
  display: flex;
  align-items: center;
  gap: 0.2rem;
}

.oh-user_permission_list-text {
  font-size: 0.75rem;
}

.oh-user_permission--profile {
  width: 46px;
  height: 46px;
}

.oh-user_permssion-dropdownbtn {
  color: hsl(0, 0%, 11%);
  background-color: transparent;
  padding: 0px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.8rem 1.25rem;
}

.oh-alert-container {
  top: 75px;
  right: 2rem;
  position: fixed;
  z-index: 9999;
  max-width: 500px;
  width: 95%;
  pointer-events: none;
}
@media (max-width: 575.98px) {
  .oh-alert-container {
    left: auto;
    right: auto;
    width: 95%;
  }
}

.oh-alert {
  opacity: 0;
  width: 100%;
  padding: 1rem;
  background-color: hsl(0, 0%, 100%);
  box-shadow: rgba(0, 0, 0, 0.04) 0px 3px 5px;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}
.oh-alert:last-child {
  margin-bottom: 0;
}
.oh-alert::before {
  content: "";
  position: relative;
  display: inline-block;
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  margin-right: 0.75rem;
}

.oh-alert--animated {
  animation: slide-right-in-pop-animation 3.5s 0s ease-in-out forwards;
}
@keyframes slide-right-in-pop-animation {
  0% {
    transform: translate3d(100%, 0, 0);
    visibility: hidden;
    opacity: 0;
  }
  30% {
    transform: translate3d(0, 0, 0);
    visibility: visible;
    opacity: 1;
  }
  80% {
    transform: translate3d(0, 0, 0);
    visibility: visible;
    opacity: 1;
  }
  100% {
    transform: translate3d(-10%, 0, 0);
    visibility: hidden;
    opacity: 0;
  }
}

.oh-alert--danger {
  background-color: hsl(0, 75%, 97%);
  border: 1px solid hsl(357, 72%, 89%);
  border-left: 5px solid hsl(0, 71%, 54%);
}
.oh-alert--danger::before {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iNDYiIGZpbGw9IiNkZDM2MzYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMTIgMi4yNWMtNS4zNzYgMC05Ljc1IDQuMzc0LTkuNzUgOS43NXM0LjM3NCA5Ljc1IDkuNzUgOS43NSA5Ljc1LTQuMzc0IDkuNzUtOS43NVMxNy4zNzYgMi4yNSAxMiAyLjI1Wm0uOTM4IDE0Ljk5NmgtMS44NzZWMTUuMzdoMS44NzZ2MS44NzVabS0uMTg4LTIuOTk2aC0xLjVsLS4yODEtNy41aDIuMDYybC0uMjgxIDcuNVoiPjwvcGF0aD4KPC9zdmc+");
}

.oh-alert--warning {
  background-color: hsl(48, 100%, 94%);
  border: 1px solid hsl(46, 97%, 88%);
  border-left: 5px solid hsl(44, 89%, 62%);
}
.oh-alert--warning::before {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iNDYiIGZpbGw9IiNmNGM3NDkiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMTIgMi4yNWMtNS4zNzYgMC05Ljc1IDQuMzc0LTkuNzUgOS43NXM0LjM3NCA5Ljc1IDkuNzUgOS43NSA5Ljc1LTQuMzc0IDkuNzUtOS43NVMxNy4zNzYgMi4yNSAxMiAyLjI1Wm0uOTM4IDE0Ljk5NmgtMS44NzZWMTUuMzdoMS44NzZ2MS44NzVabS0uMTg4LTIuOTk2aC0xLjVsLS4yODEtNy41aDIuMDYybC0uMjgxIDcuNVoiPjwvcGF0aD4KPC9zdmc+");
}

.oh-alert--info {
  background-color: hsl(212, 89%, 96%);
  border: 1px solid hsl(218, 75%, 87%);
  border-left: 5px solid hsl(225, 72%, 48%);
  color: hsl(225, 72%, 33%);
}
.oh-alert--info::before {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iNDYiIGZpbGw9IiMwNDQ0Y2UiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMTIgMi4yNWMtNS4zNzYgMC05Ljc1IDQuMzc0LTkuNzUgOS43NXM0LjM3NCA5Ljc1IDkuNzUgOS7NSA5Ljc1LTQuMzc0IDkuNzUtOS43NVMxNy4zNzYgMi4yNSAxMiAyLjI1Wm0uOTM4IDE0Ljk5NmgtMS44NzZWMTUuMzdoMS44NzZ2MS44NzVabS0uMTg4LTIuOTk2aC0xLjVsLS4yODEtNy41aDIuMDYybC0uMjgxIDcuNVoiPjwvcGF0aD4KPC9zdmc+");
}

.oh-alert--success {
  background-color: hsl(137, 78%, 93%);
  border: 1px solid hsl(136, 51%, 84%);
  border-left: 5px solid hsl(119, 39.1304347826%, 46%);
}
.oh-alert--success::before {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iNDYiIGZpbGw9IiM0OWEyNDgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMTIgMi4yNWMtNS4zNzYgMC05Ljc1IDQuMzc0LTkuNzUgOS43NXM0LjM3NCA5Ljc1IDkuNzUgOS43NSA5Ljc1LTQuMzc0IDkuNzUtOS43NVMxNy4zNzYgMi4yNSAxMiAyLjI1Wm0uOTM4IDE0Ljk5NmgtMS44NzZWMTUuMzdoMS44NzZ2MS44NzVabS0uMTg4LTIuOTk2aC0xLjVsLS4yODEtNy41aDIuMDYybC0uMjgxIDcuNVoiPjwvcGF0aD4KPC9zdmc+");
}

.oh-alert--info.mb-2 {
  padding: 0.5rem 0.75rem;
}
.oh-alert--info.mb-2 .d-flex span {
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(225, 72%, 28%);
}
.oh-alert--info.mb-2 .d-flex span .oh-icon {
  font-size: 1rem;
  margin-right: 0.5rem;
  color: hsl(225, 72%, 48%);
}
.oh-alert--info.mb-2 .d-flex .oh-btn--secondary.oh-btn--sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
  font-weight: 500;
  border-radius: 4px;
  background-color: hsl(225, 72%, 83%);
  color: hsl(225, 72%, 33%);
  border: 1px solid hsl(225, 72%, 73%);
  text-decoration: none;
  transition: all 0.2s ease;
}
.oh-alert--info.mb-2 .d-flex .oh-btn--secondary.oh-btn--sm:hover {
  background-color: hsl(225, 72%, 78%);
  border-color: hsl(225, 72%, 68%);
  color: hsl(225, 72%, 28%);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.oh-alert--info.mb-2 .d-flex .oh-btn--secondary.oh-btn--sm:active {
  transform: translateY(0);
}
.oh-alert--info.mb-2::before {
  display: none;
}

.oh-badge {
  display: inline-block;
  padding: 0.25rem;
  text-align: center;
}

.oh-badge--small {
  width: 22px;
  height: 22px;
  font-size: 0.75rem;
}

.oh-badge--round {
  display: flex;
  justify-content: center;
  border-radius: 50%;
}

.oh-tabs__input-badge-container {
  display: flex;
  align-items: center;
}

.oh-badge--danger {
  background-color: hsl(1, 100%, 61%);
  color: hsl(0, 0%, 100%);
}

.oh-badge--secondary {
  background-color: hsl(8, 77%, 56%);
  color: hsl(0, 0%, 100%);
}

.oh-badge--info {
  background-color: hsl(0, 0%, 27%);
  color: hsl(0, 0%, 100%);
}

/* ==============================
*            DOTS
* ============================= */
.oh-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.oh-dot--small {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.oh-dot--danger {
  background-color: hsl(1, 100%, 61%);
}

.oh-dot--success {
  background-color: hsl(148, 71%, 44%);
}

.oh-dot--info {
  background-color: hsl(204, 70%, 53%);
}

.oh-dot--warning {
  background-color: hsl(40, 91%, 60%);
}

/* ==============================
*       CHECKPOINT BADGE
* ============================= */
.oh-checkpoint-badge {
  border: 2px solid;
  display: inline-block;
  border-radius: 4px;
  padding: 0.3rem 0.5rem;
  font-weight: bold;
  margin-left: auto;
  margin-right: auto;
  font-size: 0.8rem;
}

.oh-checkpoint-badge--secondary {
  color: hsl(8, 77%, 56%);
  border-color: hsl(8, 77%, 56%);
}

.oh-file-icon--html {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iNDYiIGZpbGw9IiNlNTRmMzkiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KIDxwYXRoIGQ9Im0xMiAxOC4xNzggNC42Mi0xLjI1Ni42MjMtNi43NzhIOS4wMjZMOC44MjIgNy44OWg4LjYyNmwuMjI3LTIuMjExSDYuMzI1bC42MzYgNi42NzhoNy44MmwtLjI2MSAyLjg2Ni0yLjUyLjY2Ny0yLjUyLS42NjctLjE1OC0xLjg0NGgtMi4yN2wuMzI5IDMuNTQ0TDEyIDE4LjE3OFpNMyAyaDE4bC0xLjYyMyAxOEwxMiAyMmwtNy4zNzctMkwzIDJaIj48L3BhdGg+Cjwvc3ZnPg==");
}

.oh-file-icon--excel {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iNDYiIGZpbGw9IiNlNTRmMzkiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KIDxwYXRoIGQ9Im0yLjg1OSAyLjg3OCAxMi41Ny0xLjc5NWEuNS41IDAgMCAxIC41NzEuNDk1djIwLjg0NmEuNS41IDAgMCAxLS41Ny40OTVMMi44NTggMjEuMTI0YTEgMSAwIDAgMS0uODU5LS45OVYzLjg2OGExIDEgMCAwIDEgLjg1OS0uOTloLjAwMVpNMTcgMy4wMDFoNGExIDEgMCAwIDEgMSAxdjE2YTEgMSAwIDAgMS0xIDFoLTR2LTE4Wm0tNi44IDkgMi44LTRoLTIuNEw5IDEwLjI4NyA3LjQgOC4wMDFINWwyLjggNC0yLjggNGgyLjRMOSAxMy43MTVsMS42IDIuMjg2SDEzbC0yLjgtNFoiPjwvcGF0aD4KPC9zdmc+");
}

.oh-file-icon--word {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iNDYiIGZpbGw9IiNlNTRmMzkiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KIDxwYXRoIGQ9Ik0xMi4xODYgMTQuNTU1Yy0uNjE3IDAtLjk3Ny41ODctLjk3NyAxLjM3MyAwIC43OS4zNzEgMS4zNS45ODMgMS4zNS42MTcgMCAuOTcxLS41ODguOTcxLTEuMzc0IDAtLjcyNi0uMzQ4LTEuMzUtLjk3Ny0xLjM1WiI+PC9wYXRoPgogPHBhdGggZD0iTTE0IDJINmEyIDIgMCAwIDAtMiAydjE2YTIgMiAwIDAgMCAyIDJoMTJhMiAyIDAgMCAwIDItMlY4bC02LTZaTTkuMTU1IDE3LjQ1NGMtLjQyNi4zNTQtMS4wNzMuNTIxLTEuODY0LjUyMS0uNDc1IDAtLjgxLS4wMy0xLjAzOC0uMDZ2LTMuOTcxYTguMTY1IDguMTY1IDAgMCAxIDEuMjM1LS4wODNjLjc2OCAwIDEuMjY2LjEzOCAxLjY1NS40MzIuNDIuMzEyLjY4NC44MS42ODQgMS41MjIgMCAuNzc1LS4yODIgMS4zMDktLjY3MiAxLjYzOVptMi45OS41NDZjLTEuMiAwLTEuOTAxLS45MDYtMS45MDEtMi4wNTggMC0xLjIxMS43NzMtMi4xMTYgMS45NjctMi4xMTYgMS4yNDEgMCAxLjkxOS45MjkgMS45MTkgMi4wNDUtLjAwMSAxLjMyNS0uODA1IDIuMTI5LTEuOTg1IDIuMTI5Wm00LjY1NS0uNzYyYy4yNzUgMCAuNTgxLS4wNjEuNzYyLS4xMzJsLjEzOC43MTNjLS4xNjguMDg0LS41NDYuMTc0LTEuMDM3LjE3NC0xLjM5NyAwLTIuMTE3LS44NjktMi4xMTctMi4wMjEgMC0xLjM3OS45ODMtMi4xNDYgMi4yMDctMi4xNDYuNDc0IDAgLjgzMy4wOTYuOTk1LjE4bC0uMTg2LjcyNmExLjk4IDEuOTggMCAwIDAtLjc2OC0uMTVjLS43MjYgMC0xLjI5LjQzOC0xLjI5IDEuMzM4IDAgLjgwOS40OCAxLjMxOCAxLjI5NiAxLjMxOFpNMTQgOWgtMVY0bDUgNWgtNFoiPjwvcGF0aD4KIDxwYXRoIGQ9Ik03LjU4NCAxNC41NjNjLS4yMDMgMC0uMzM1LjAxOC0uNDEzLjAzNXYyLjY0NmMuMDc4LjAxOC4yMDQuMDE4LjMxNy4wMTguODI4LjAwNSAxLjM2Ny0uNDUgMS4zNjctMS40MTUuMDA2LS44NC0uNDg1LTEuMjg0LTEuMjcxLTEuMjg0WiI+PC9wYXRoPgo8L3N2Zz4=");
}

.oh-file-icon--default {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iNDYiIGZpbGw9IiNlNTRmMzkiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KIDxwYXRoIGQ9Ik0xOCAyMmEyIDIgMCAwIDAgMi0yVjhsLTYtNkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMlpNMTMgNGw1IDVoLTVWNFpNNyA4aDN2Mkg3VjhabTAgNGgxMHYySDd2LTJabTAgNGgxMHYySDd2LTJaIj48L3BhdGg+Cjwvc3ZnPg==");
}

.oh-file-icon--powerpoint {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iNDYiIGZpbGw9IiNlNTRmMzgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJtMTYgMiA1IDV2MTQuMDA4YS45OTMuOTkzIDAgMCAxLS45OTMuOTkySDMuOTkzQTEgMSAwIDAgMSAzIDIxLjAwOFYyLjk5MkMzIDIuNDQ0IDMuNDQ1IDIgMy45OTMgMkgxNlpNOCA4djhoMnYtMmg2VjhIOFptMiAyaDR2MmgtNHYtMloiPjwvcGF0aD4KPC9zdmc+");
}

.oh-btn {
  display: block;
  border: none;
  border-radius: 0rem;
  padding: 0.8rem 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  color: hsl(219, 9%, 29%);
}
@media (max-width: 575.98px) {
  .oh-btn {
    font-size: 0.8rem !important;
  }
}
.oh-btn:hover {
  background-color: hsl(0, 0%, 90%);
}
.oh-btn:focus, .oh-btn:focus-visible {
  outline: hsl(0, 0%, 13%) solid 2px;
  border-radius: 0;
  border: none;
}
.oh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.oh-btn--shadow {
  filter: drop-shadow(0px 1px 4px rgba(0, 0, 0, 0.1));
}

.oh-btn--sq-sm {
  width: 28px;
  height: 28px;
}

.oh-btn--inline {
  display: inline-block;
}

.oh-btn--transparent {
  background-color: transparent;
  color: rgba(28.05, 28.05, 28.05, 0.8);
}
.oh-btn--transparent:hover, .oh-btn--transparent:focus, .oh-btn--transparent:focus-visible {
  background-color: transparent;
  border: none;
  color: rgb(28.05, 28.05, 28.05);
}

.oh-btn--secondary {
  background-color: hsl(8, 77%, 56%);
  color: hsl(0, 0%, 100%);
  text-decoration: none;
}
.oh-btn--secondary:hover, .oh-btn--secondary:focus, .oh-btn--secondary:focus-visible {
  background-color: hsl(8, 61%, 50%);
  color: white;
  text-decoration: none;
}
.oh-btn--secondary:disabled {
  background-color: hsl(8, 77%, 56%);
}

.oh-btn--secondary-ouline {
  border: 1px solid hsl(8, 77%, 56%);
  color: hsl(8, 77%, 56%);
  background-color: transparent;
}
.oh-btn--secondary-ouline:hover {
  background-color: hsl(8, 77%, 56%);
  color: hsl(0, 0%, 100%);
}
.oh-btn--secondary-ouline:focus, .oh-btn--secondary-ouline:focus-visible {
  color: hsl(8, 61%, 50%);
  background-color: transparent;
  border: 1px solid hsl(8, 61%, 50%);
}

.oh-btn--success {
  background-color: hsl(148, 71%, 44%);
  color: hsl(0, 0%, 100%);
}
.oh-btn--success:hover, .oh-btn--success:focus, .oh-btn--success:focus-visible {
  background-color: hsl(148, 70%, 40%);
  text-decoration: none;
}

.oh-btn--danger {
  background-color: hsl(1, 100%, 61%);
  color: hsl(0, 0%, 100%);
}
.oh-btn--danger:hover, .oh-btn--danger:focus, .oh-btn--danger:focus-visible {
  background-color: hsl(1, 64%, 49%);
  text-decoration: none;
}

.oh-btn--danger-outline {
  color: hsl(1, 100%, 61%);
}
.oh-btn--danger-outline:hover, .oh-btn--danger-outline:focus, .oh-btn--danger-outline:focus-visible {
  color: hsl(1, 64%, 49%);
  text-decoration: none;
}

.oh-btn--primary {
  background-color: hsl(0, 0%, 13%);
  color: hsl(0, 0%, 100%);
}
.oh-btn--primary:hover, .oh-btn--primary:focus, .oh-btn--primary:focus-visible {
  background-color: hsl(0, 0%, 12%);
  color: hsl(0, 0%, 100%);
  text-decoration: none;
}

.oh-btn--primary-outline {
  background-color: transparent;
  border: 1px solid hsl(0, 0%, 13%);
  color: hsl(0, 0%, 13%);
}
.oh-btn--primary-outline:hover, .oh-btn--primary-outline:focus, .oh-btn--primary-outline:focus-visible {
  background-color: hsl(0, 0%, 13%);
  color: hsl(0, 0%, 100%);
}

.oh-btn--secondary-outline {
  background-color: transparent;
  border: 1px solid hsl(8, 77%, 56%);
  color: hsl(8, 77%, 56%);
}
.oh-btn--secondary-outline:hover, .oh-btn--secondary-outline:focus, .oh-btn--secondary-outline:focus-visible {
  background-color: hsl(8, 77%, 56%);
  color: hsl(0, 0%, 100%);
}

.oh-btn--white-outline {
  background-color: transparent;
  border: 1px solid hsl(0, 0%, 100%);
  color: hsl(0, 0%, 100%);
}
.oh-btn--white-outline:hover, .oh-btn--white-outline:focus, .oh-btn--white-outline:focus-visible {
  background-color: hsl(0, 0%, 100%);
  color: hsl(8, 77%, 56%);
}

.oh-btn--warning-outline {
  background-color: transparent;
  border: 1px solid hsl(40, 91%, 60%);
  color: hsl(40, 91%, 60%);
}
.oh-btn--warning-outline:hover, .oh-btn--warning-outline:focus, .oh-btn--warning-outline:focus-visible {
  background-color: hsl(40, 91%, 60%);
  color: hsl(0, 0%, 11%);
}

.oh-btn--success-outline {
  background-color: transparent;
  border: 1px solid hsl(148, 71%, 44%);
  color: hsl(148, 71%, 44%);
}
.oh-btn--success-outline:hover, .oh-btn--success-outline:focus, .oh-btn--success-outline:focus-visible {
  background-color: hsl(148, 71%, 44%);
  color: hsl(0, 0%, 100%);
}

.oh-btn--info-outline {
  background-color: transparent;
  border: 1px solid hsl(204, 70%, 53%);
  color: hsl(204, 70%, 53%);
}
.oh-btn--info-outline:hover, .oh-btn--info-outline:focus, .oh-btn--info-outline:focus-visible {
  background-color: hsl(204, 70%, 53%);
  color: hsl(0, 0%, 100%);
}

.oh-btn--info {
  background-color: hsl(204, 70%, 53%);
  color: hsl(0, 0%, 100%);
}
.oh-btn--info:hover, .oh-btn--info:focus, .oh-btn--info:focus-visible {
  background-color: hsl(204, 62%, 48%);
  color: hsl(0, 0%, 100%);
  text-decoration: none;
}

.oh-btn--warning {
  background-color: hsl(40, 91%, 60%);
  color: hsl(0, 0%, 11%);
}
.oh-btn--warning:hover, .oh-btn--warning:focus, .oh-btn--warning:focus-visible {
  background-color: #dda735;
  color: hsl(0, 0%, 11%);
  text-decoration: none;
}

.oh-btn--dropdown {
  position: relative;
}
.oh-btn--dropdown::after {
  content: url("data:image/svg+xml,%3Csvg width='12' height='12' fill='%23ffffff' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m4.594 8.912 6.553 7.646a1.126 1.126 0 0 0 1.708 0l6.552-7.646c.625-.73.107-1.857-.854-1.857H5.447c-.961 0-1.48 1.127-.853 1.857Z'%3E%3C/path%3E%3C/svg%3E");
  position: relative;
  width: 20px;
  left: 5px;
}

.oh-input--medium {
  font-size: 0.9rem;
  padding: 0.65rem 1rem;
  height: 40.94px;
}

.oh-btn--small {
  font-size: 0.9rem;
  padding: 0.65rem 1rem;
}

.oh-btn--x-small {
  font-size: 0.8rem;
  padding: 0.5rem 0.8rem;
}

.oh-btn--f-md {
  font-size: 1.15rem;
}

@media (max-width: 767.98px) {
  .oh-btn--w-100-resp {
    width: 100%;
  }
}

.oh-btn--secondary-link {
  color: hsl(8, 77%, 56%);
}
.oh-btn--secondary-link:hover, .oh-btn--secondary-link:focus, .oh-btn--secondary-link:focus-visible {
  color: hsl(8, 61%, 50%);
  text-decoration: none !important;
}

.oh-btn--danger-link {
  color: hsl(1, 100%, 61%);
}
.oh-btn--danger-link:hover, .oh-btn--danger-link:focus, .oh-btn--danger-link:focus-visible {
  color: hsl(1, 64%, 49%);
  text-decoration: none !important;
}

.oh-btn--light {
  background-color: hsl(0, 0%, 97.5%);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  color: hsl(8, 77%, 56%);
}
.oh-btn--light:hover, .oh-btn--light:focus, .oh-btn--light:focus-visible {
  text-decoration: none;
  color: hsl(8, 61%, 50%);
  background-color: hsl(213, 22%, 93%);
}

.oh-btn--light-dark-text {
  background-color: hsl(0, 0%, 97.5%);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  color: hsl(0, 0%, 11%);
}
.oh-btn--light-dark-text:hover, .oh-btn--light-dark-text:focus, .oh-btn--light-dark-text:focus-visible {
  text-decoration: none;
  color: hsl(0, 0%, 11%);
  background-color: hsl(213, 22%, 93%);
}

.oh-btn--light-danger {
  color: hsl(1, 100%, 61%);
  background-color: transparent;
}
.oh-btn--light-danger:hover, .oh-btn--light-danger:focus, .oh-btn--light-danger:focus-visible {
  text-decoration: none;
  color: hsl(1, 64%, 49%);
}

.oh-btn--view {
  background-color: hsl(0, 0%, 100%);
  border: none;
}
.oh-btn--view:hover, .oh-btn--view:focus, .oh-btn--view:focus-visible {
  background-color: hsl(0, 0%, 97.5%);
  outline: none;
}

.oh-btn--view-active {
  background-color: hsl(0, 0%, 93%);
  color: hsl(0, 0%, 11%);
}

.oh-btn-group {
  display: flex;
  align-items: center;
  border: 1px solid hsl(213, 22%, 93%);
}

.oh-btn-group > * {
  border-right: 1px solid hsl(213, 22%, 93%);
}
.oh-btn-group > *:last-child {
  border-right: none;
}

.oh-btn--light-bkg {
  background-color: hsl(0, 0%, 97.5%) !important;
}
.oh-btn--light-bkg:hover {
  background-color: hsl(0, 0%, 96%) !important;
}

.oh-floatin-btn {
  position: fixed;
  right: 1.5rem;
  bottom: 2.5rem;
  display: flex;
  align-items: center;
  border: none;
  border-radius: 25px;
  padding: 1rem 1.5rem;
  background-color: hsl(0, 0%, 100%);
  box-shadow: rgba(0, 0, 0, 0.1) 0px 3px 8px;
  transition: all 0.3s ease-in-out;
}
.oh-floatin-btn:hover {
  transition: all 0.3s ease-in-out;
}

.oh-floatin-btn--success:hover {
  color: hsl(148, 71%, 44%);
  transition: all 0.3s ease-in-out;
}

.oh-floatin-btn--danger:hover {
  color: hsl(1, 100%, 61%);
  transition: all 0.3s ease-in-out;
}

.oh-btn--disabled {
  opacity: 0.6;
  cursor: not-allowed !important;
}
.oh-btn--disabled:focus, .oh-btn--disabled:focus-visible {
  outline: none;
}

.oh-btn--chat {
  display: inline-flex;
  background-color: transparent;
}
.oh-btn--chat::after {
  width: 20px;
  height: 20px;
  position: absolute;
  display: block;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  transition: all 0.4s ease-in-out;
}
.oh-btn--chat:hover {
  background-color: transparent;
}
.oh-btn--chat:hover::after {
  transition: all 0.4s ease-in-out;
}

.oh-btn--chat-send::after {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='ionicon s-ion-icon' fill='darkgrey' viewBox='0 0 512 512'%3E%3Cpath d='M16 464l480-208L16 48v160l320 48-320 48z'%3E%3C/path%3E%3C/svg%3E");
}
.oh-btn--chat-send:hover::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='ionicon s-ion-icon' fill='grey' viewBox='0 0 512 512'%3E%3Cpath d='M16 464l480-208L16 48v160l320 48-320 48z'%3E%3C/path%3E%3C/svg%3E");
}

.oh-btn--chat-attachments {
  position: absolute;
}
.oh-btn--chat-attachments::after {
  content: "";
  width: 22px;
  height: 22px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='18' height='18'%3E%3Cpath fill='none' d='M0 0h24v24H0z'%3E%3C/path%3E%3Cpath d='M14.8287 7.75737L9.1718 13.4142C8.78127 13.8047 8.78127 14.4379 9.1718 14.8284C9.56232 15.219 10.1955 15.219 10.586 14.8284L16.2429 9.17158C17.4144 8.00001 17.4144 6.10052 16.2429 4.92894C15.0713 3.75737 13.1718 3.75737 12.0002 4.92894L6.34337 10.5858C4.39075 12.5384 4.39075 15.7042 6.34337 17.6569C8.29599 19.6095 11.4618 19.6095 13.4144 17.6569L19.0713 12L20.4855 13.4142L14.8287 19.0711C12.095 21.8047 7.66283 21.8047 4.92916 19.0711C2.19549 16.3374 2.19549 11.9053 4.92916 9.17158L10.586 3.51473C12.5386 1.56211 15.7045 1.56211 17.6571 3.51473C19.6097 5.46735 19.6097 8.63317 17.6571 10.5858L12.0002 16.2427C10.8287 17.4142 8.92916 17.4142 7.75759 16.2427C6.58601 15.0711 6.58601 13.1716 7.75759 12L13.4144 6.34316L14.8287 7.75737Z' fill='rgba(169,169,169,1)'%3E%3C/path%3E%3C/svg%3E");
}
.oh-btn--chat-attachments:hover::after {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='18' height='18'%3E%3Cpath fill='none' d='M0 0h24v24H0z'%3E%3C/path%3E%3Cpath d='M14.8287 7.75737L9.1718 13.4142C8.78127 13.8047 8.78127 14.4379 9.1718 14.8284C9.56232 15.219 10.1955 15.219 10.586 14.8284L16.2429 9.17158C17.4144 8.00001 17.4144 6.10052 16.2429 4.92894C15.0713 3.75737 13.1718 3.75737 12.0002 4.92894L6.34337 10.5858C4.39075 12.5384 4.39075 15.7042 6.34337 17.6569C8.29599 19.6095 11.4618 19.6095 13.4144 17.6569L19.0713 12L20.4855 13.4142L14.8287 19.0711C12.095 21.8047 7.66283 21.8047 4.92916 19.0711C2.19549 16.3374 2.19549 11.9053 4.92916 9.17158L10.586 3.51473C12.5386 1.56211 15.7045 1.56211 17.6571 3.51473C19.6097 5.46735 19.6097 8.63317 17.6571 10.5858L12.0002 16.2427C10.8287 17.4142 8.92916 17.4142 7.75759 16.2427C6.58601 15.0711 6.58601 13.1716 7.75759 12L13.4144 6.34316L14.8287 7.75737Z' fill='rgba(91,91,91,1)'%3E%3C/path%3E%3C/svg%3E");
}

.oh-btn--sq {
  width: 32px;
  height: 32px;
  padding: 0;
}

.oh-btn--danger-text {
  color: hsl(1, 100%, 61%);
}
.oh-btn--danger-text:hover {
  color: hsl(1, 64%, 49%);
}

.oh-hover-btn {
  border: 2px solid rgb(28.05, 28.05, 28.05);
  background-color: transparent;
  border-radius: 3px;
  padding: 0.15rem 0.4rem;
}
.oh-hover-btn:hover {
  background-color: hsl(213, 22%, 93%);
}

.oh-hover-btn-container {
  position: relative;
  display: inline-block;
}
.oh-hover-btn-container::before {
  content: "";
  display: block;
  background-color: transparent;
  position: absolute;
  width: 100%;
  height: 20px;
  bottom: 100%;
}

.oh-hover-btn__small {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: hsl(0, 0%, 100%);
  border: 1px solid hsl(213, 22%, 93%);
  box-shadow: rgba(17, 17, 26, 0.05) 0px 4px 16px, rgba(17, 17, 26, 0.05) 0px 8px 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s linear;
}
.oh-hover-btn__small:hover {
  background-color: hsl(8, 77%, 56%);
  border: 1px solid hsl(8, 61%, 50%);
  color: hsl(0, 0%, 100%);
  transition: all 0.2s linear;
  transform: scale(105%);
}

.oh-hover-btn-drawer {
  align-items: center;
  position: absolute;
  display: flex;
  gap: 5px;
  z-index: 99;
  right: -20px;
  bottom: 130%;
  visibility: hidden;
  pointer-events: none;
  opacity: 0;
}

.oh-hover-btn-container:hover .oh-hover-btn-drawer {
  display: flex;
  visibility: visible;
  pointer-events: all;
  opacity: 1;
}

.oh-card {
  padding: 1.15rem;
  border-radius: 0rem;
  border: 1px solid hsl(213, 22%, 84%);
  background-color: hsl(0, 0%, 100%);
}

.oh-card--margin {
  margin-top: 2rem;
}

@media (max-width: 767.98px) {
  .oh-card.p-4 {
    padding: 0 !important;
  }
}

.oh-card--no-shadow {
  box-shadow: none !important;
  border: 1px solid hsl(213, 22%, 93%) !important;
}

.oh-card__header {
  margin-bottom: 0.85rem;
}

.oh-card__title--lg {
  font-size: 1.25rem;
}

.oh-card__title--sm {
  font-size: 0.9rem;
}

.oh-card--w-resp-75 {
  width: 75%;
}
@media (max-width: 991.98px) {
  .oh-card--w-resp-75 {
    width: 100%;
  }
}

/* ============================
        LIST CARDS
============================= */
.oh-list-cards {
  list-style-type: none;
  padding-left: 0px;
  margin-bottom: 0px;
}

.oh-list-cards__item {
  padding: 1.25rem;
  border: 1px solid hsl(213, 22%, 93%);
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  border-radius: 0rem;
}
.oh-list-cards__item:last-child {
  margin-bottom: 0;
}

.oh-list-card__title {
  font-weight: bold;
}

.oh-list-card__description {
  color: hsl(0, 0%, 45%);
}

.oh-list-card__footer {
  border-top: 1px solid hsl(213, 22%, 93%);
  padding-top: 0.8rem;
}

.oh-list__actions {
  padding-top: 1rem;
  float: right;
}
.oh-list__actions a:first-child {
  margin-right: 0.5rem;
}

.oh-card__footer--border-top {
  border-top: 1px solid hsl(213, 22%, 93%);
}

/* ============================
      ACTIVITY LIST CARDS
============================= */
.oh-activity-list__photo {
  border-radius: 50%;
  overflow: hidden;
}

.oh-activity-list {
  list-style-type: none;
  padding-left: 0;
}

.oh-activity-list__photo--small {
  width: 30px;
  height: 30px;
}

.oh-activity-list__image {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.oh-activity-list__item {
  display: flex;
  align-items: center;
  margin-top: 1.15rem;
  margin-bottom: 1.15rem;
}
.oh-activity-list__item:first-child {
  margin-top: 0;
}
.oh-activity-list__item:last-child {
  margin-bottom: 0;
}
.oh-activity-list__item * {
  flex-shrink: 0;
}

.oh-activity-list__description {
  color: hsl(0, 0%, 45%);
  max-width: calc(100% - 32px);
}

.oh-activity-list__description strong {
  color: hsl(0, 0%, 27%);
}

.oh-card--border {
  border: 1px solid hsl(213, 22%, 93%);
}

.oh-activity-list__comment-title {
  display: flex;
  align-items: flex-start;
}

.oh-activity-list__comment-timestamp {
  font-size: 0.75rem;
  display: block;
}

.oh-activity-list__comment {
  display: block;
}

.oh-activity-list__comment-container {
  padding-left: 2.3rem;
  margin-top: 0.45rem;
  margin-bottom: 1.35rem;
}

/* ============================
*         ITEM CARD
============================= */
.oh-item {
  display: block;
}

.oh-item--border-bottom {
  padding: 1.25rem;
  border-bottom: 1px solid hsl(213, 22%, 93%);
}
.oh-item--border-bottom:last-child {
  border-bottom: none;
}

/* ============================
*      CONTAINER CARD
============================= */
.oh-container {
  width: 100%;
  padding: 0.75rem;
}

.oh-container--outline {
  border: 1px solid hsl(213, 22%, 93%);
}

.oh-kanban-card {
  position: relative;
  border: 1px solid hsl(213, 22%, 84%);
  background-color: hsl(0, 0%, 100%);
  display: flex;
  align-items: center;
  padding: 1rem;
  width: 100%;
  cursor: pointer;
}
.oh-kanban-card:hover {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.02) 0px 1px 2px 0px;
}
.oh-kanban-card--red {
  border-left: 5px solid hsl(1, 64%, 49%);
}
.oh-kanban-card--blue {
  border-left: 5px solid hsl(204, 70%, 53%);
}
.oh-kanban-card--yellow {
  border-left: 5px solid #dda735;
}
.oh-kanban-card--green {
  border-left: 5px solid hsl(148, 70%, 40%);
}
.oh-kanban-card--orange {
  border-left: 5px solid hsl(19, 85%, 63%);
}

.oh-kanban-card--biometric {
  padding-left: 1.5rem;
  flex-direction: column;
  align-items: flex-start;
}

.oh-kanban-card__biometric-actions {
  padding-top: 0.75rem;
  margin-top: 0.75rem;
  display: block;
  width: 100%;
  border-top: 1px solid hsl(213, 22%, 93%);
}

.oh-kanban-card__profile-container {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
}

.oh-kanban-card__profile-image {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.oh-kanban-card__title {
  display: block;
  font-weight: bold;
}
@media (max-width: 575.98px) {
  .oh-kanban-card__title {
    font-size: 0.8rem;
  }
}

.oh-kanban-card__subtitle {
  font-size: 0.85rem;
  color: hsl(0, 0%, 45%);
}
@media (max-width: 575.98px) {
  .oh-kanban-card__subtitle {
    font-size: 0.7rem;
  }
}

.oh-kanban-card__avatar {
  margin-right: 1rem;
}

.oh-kanban-card__actions {
  display: flex;
  align-items: center;
}

.oh-kanban-card__actions > * {
  margin-right: 0.5rem;
  flex-shrink: 0;
}
.oh-kanban-card__actions > *:last-child {
  margin-right: 0;
}

.oh-kanban-card__dots {
  position: absolute;
  right: 0px;
  top: 0px;
}

.oh-dropdown {
  position: relative;
}

.oh-dropdown__menu,
.oh-dropdown__import {
  position: absolute;
  min-width: 200px;
  background-color: hsl(0, 0%, 100%);
  padding: 0px 15px;
  border-radius: 0rem;
  color: hsl(0, 0%, 11%);
  border: 1px solid hsl(0, 0%, 95%);
  box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
  z-index: 999;
}

.oh-dropdown__menu--hidden {
  display: none;
}

.oh-dropdown__menu--right,
.oh-dropdown__import--right {
  right: 0;
}

.oh-dropdown__import {
  width: 350px;
  padding-top: 1rem;
  padding-bottom: 1rem;
}
@media (max-width: 575.98px) {
  .oh-dropdown__import {
    width: 95%;
    position: fixed;
    left: 10px;
    right: unset;
  }
}

.oh-dropdown__import-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100px;
  border: 1px dashed hsl(213, 22%, 84%);
  padding: 1rem;
  background: rgba(233.223, 236.7573, 241.077, 0.2);
  cursor: pointer;
}
.oh-dropdown__import-label:hover {
  background: rgba(233.223, 236.7573, 241.077, 0.4);
}

.oh-dropdown__import-form-icon {
  font-size: 4rem;
  color: rgba(28.05, 28.05, 28.05, 0.85);
}

.oh-dropdown__import-form-title {
  font-size: 1rem;
  font-weight: bold;
  color: rgba(28.05, 28.05, 28.05, 0.85);
}

.oh-dropdown__import-form-text {
  font-size: 0.85rem;
  color: hsl(0, 0%, 45%);
}

.oh-dropdown__menu--top-100 {
  top: 100%;
}

.oh-dropdown__menu hr {
  border-style: solid;
  border-color: hsl(0, 0%, 95%);
}

.oh-dropdown__items {
  list-style-type: none;
  padding-left: 0;
}

.oh-dropdown__item {
  margin-top: 15px;
  margin-bottom: 15px;
}

.oh-dropdown__link {
  color: hsl(0, 0%, 11%) !important;
  display: block;
  width: 100%;
  height: 100%;
}
.oh-dropdown__link:hover {
  color: hsl(8, 77%, 56%) !important;
  text-decoration: none !important;
}
@media (max-width: 575.98px) {
  .oh-dropdown__link {
    font-size: 0.85rem;
  }
}

.oh-dropdown__link--danger {
  color: hsl(1, 100%, 61%) !important;
  border: none;
  background-color: transparent;
  padding: 0;
  width: unset;
  text-align: left;
}
.oh-dropdown__link--danger:hover {
  color: rgba(255, 59.415, 56.1, 0.9) !important;
}

.oh-dropdown__footer {
  display: flex;
  align-items: center;
}
.oh-dropdown__footer * {
  flex-shrink: 0;
}

.oh-dropdown__menu--dark-border {
  border: 1px solid hsl(213, 22%, 84%);
}

.oh-dropdown__menu--highlight {
  border-color: hsl(213, 22%, 84%);
  box-shadow: rgba(0, 0, 0, 0.1) -4px 9px 25px -6px;
}

.oh-tabs__action-dropdown {
  min-width: 300px;
  max-width: 400px;
  padding: 0.25rem;
}

.oh-dropdown__header {
  font-weight: bold;
  display: block;
  width: 100%;
  border-bottom: 1px solid rgba(233.223, 236.7573, 241.077, 0.4);
  padding-bottom: 0.4rem;
  margin-bottom: 1rem;
}

.oh-dropdown__filter {
  min-width: 500px;
}
@media (max-width: 767.98px) {
  .oh-dropdown__filter {
    min-width: 250px;
    left: 0;
    right: unset;
  }
}

@media (min-width: 1200px) {
  .oh-dropdown__menu--top-align {
    bottom: 0;
    right: 130px;
    transform: translateY(20%);
  }
}

.oh-dropdown__lang-icon {
  width: 22px;
  height: 22px;
  margin-right: 10px;
}

input[type=checkbox] {
  accent-color: hsl(8, 77%, 56%);
}

.oh-input {
  border: 1px solid hsl(213, 22%, 84%);
  border-radius: 0rem;
  padding: 0.8rem 1.25rem;
  color: hsl(0, 0%, 11%);
}
.oh-input:-moz-read-only {
  border: none;
}
.oh-input:read-only {
  border: none;
}
.oh-input:-moz-read-only:focus, .oh-input:-moz-read-only:focus-visible {
  outline: none;
  border: none !important;
}
.oh-input:read-only:focus, .oh-input:read-only:focus-visible {
  outline: none;
  border: none !important;
}

.oh-input::-moz-placeholder, select::-moz-placeholder {
  color: hsl(216, 18%, 64%);
}

.oh-input::placeholder,
select::placeholder {
  color: hsl(216, 18%, 64%);
}
@media (max-width: 575.98px) {
  .oh-input::-moz-placeholder, select::-moz-placeholder {
    font-size: 0.8rem;
  }
  .oh-input::placeholder,
  select::placeholder {
    font-size: 0.8rem;
  }
}
.oh-input:focus, .oh-input:focus-visible,
select:focus,
select:focus-visible {
  outline: hsl(0, 0%, 13%) solid 1px;
  border: 1px solid hsl(0, 0%, 13%) !important;
}

.oh-input--textarea {
  width: 100%;
  padding: 0.8rem 1.25rem !important;
}

.oh-input:disabled {
  background-color: transparent;
  padding-left: 0px;
  padding-right: 0px;
}

.oh-input--small {
  padding: 0.25rem 0.8rem;
  font-size: 0.85rem;
}

.oh-input--filter,
.oh-dropdown__filter-body .oh-select--sm + .select2-container .select2-selection--single {
  font-size: 0.85rem !important;
  height: 28px !important;
}

.oh-input--res-height {
  height: 38px;
}

.oh-input--block {
  width: 100%;
}

.oh-input__icon {
  padding-left: 28px;
}

.oh-input-group {
  position: relative;
}

.oh-input-group__icon {
  position: relative;
  color: hsl(216, 3%, 39%);
}

.oh-input-group__icon--left {
  top: 3px;
  left: 30px;
}

@media (max-width: 1000px) {
  .oh-input__search-group {
    display: none;
    position: absolute;
    padding: 15px;
    background: white;
    z-index: 99;
    right: 0;
    top: 150px;
    border-radius: 0rem;
  }
}

@media (max-width: 1000px) {
  .oh-input__search-group--show {
    display: block !important;
  }
}

.oh-select {
  border: 1px solid hsl(213, 22%, 84%);
  padding: 0.3rem 0.8rem 0.3rem 0.3rem;
  border-radius: 0rem;
  background-color: #fff;
}

.oh-select--sm,
.oh-select--xs-forced {
  font-size: 0.8rem;
  padding-top: 0.25rem;
}

.oh-select--xs-forced + .select2-container .select2-selection.select2-selection--single {
  height: 29.03px !important;
  font-size: 0.8rem !important;
}

.oh-select--xs + .select2-container .select2-selection--single {
  height: 29.03px !important;
  font-size: 0.8rem !important;
}

.oh-select--xs + .select2.select2-container .select2-selection__choice {
  font-size: 0.8rem !important;
}

.oh-select--dropdown {
  width: 100%;
  padding: 0.35rem 0.25rem;
  margin-top: 0.5rem;
  margin-bottom: 0.15rem;
  font-size: 0.9rem;
  border-radius: 0rem;
  height: 32px;
}
.oh-select--dropdown:first-child {
  margin-top: 0;
}
.oh-select--dropdown:last-child {
  margin-bottom: 0;
}

.oh-input--dropdown {
  width: 100%;
  font-size: 0.9rem;
  padding: 0.35rem 0.25rem;
  border-radius: 0rem;
  margin-top: 0.6rem;
  height: 32px;
}

.oh-input__label {
  display: block;
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  margin-top: 1rem;
}

.oh-label {
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  margin-top: 1rem;
}

.oh-info {
  display: flex;
  align-items: center;
  position: relative;
  top: 3px;
  left: 3px;
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIGZpbGw9IiM5YzljOWMiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi40ODYgMiAyIDYuNDg2IDIgMTJzNC40ODYgMTAgMTAgMTAgMTAtNC40ODYgMTAtMTBTMTcuNTE0IDIgMTIgMlptMSAxNWgtMnYtNmgydjZabTAtOGgtMlY3aDJ2MloiPjwvcGF0aD4KPC9zdmc+");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  cursor: pointer;
}

.oh-label__info {
  display: flex;
  align-items: center;
}

.oh-label--question {
  display: block;
  font-weight: bold;
}

.oh-editable-input {
  width: auto;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  background-color: transparent;
}

select.oh-select--lg {
  padding: 0.8rem 1.25rem 0.8rem 0.25rem;
}

.oh-input-picker-group {
  display: flex;
  align-items: center;
}

@media (max-width: 767.98px) {
  .oh-input-picker-group--resp {
    flex-direction: column;
  }
}

.oh-input-picker {
  display: inline-block;
  padding: 10px;
  border: 1px solid hsl(213, 22%, 93%);
  cursor: pointer;
}
.oh-input-picker input {
  display: none;
}
.oh-input-picker:first-child {
  border-radius: 0rem 0rem 0rem 0rem;
  border-right: none;
}
.oh-input-picker:last-child {
  border-radius: 0rem 0rem 0rem 0;
  border-left: none;
}

.oh-input-picker--sm {
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.oh-input-picker--likert {
  font-size: 0.85rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
@media (max-width: 767.98px) {
  .oh-input-picker--likert {
    width: 100%;
    text-align: center;
  }
}

.oh-input-picker--1.oh-input-picker--selected {
  background-color: hsl(1, 100%, 61%);
}

.oh-input-picker--2.oh-input-picker--selected {
  background-color: hsl(19, 85%, 63%);
}

.oh-input-picker--3.oh-input-picker--selected {
  background-color: hsl(40, 91%, 60%);
}

.oh-input-picker--4.oh-input-picker--selected {
  background-color: hsl(121, 47%, 61%);
}

.oh-input-picker--5.oh-input-picker--selected {
  background-color: hsl(148, 70%, 40%);
}

.oh-input-picker--selected {
  background-color: hsl(8, 77%, 56%);
  color: hsl(0, 0%, 100%);
}

.oh-input-picker-selected {
  border: 1px solid hsl(213, 22%, 93%);
}

.oh-input--file {
  padding: 1rem;
}

.oh-input--file-sm {
  max-width: 250px;
}

.oh-select-image ~ .select2-container .select2-container--default,
.oh-select-image ~ .select2-container .select2-selection--single {
  border: none;
}

.oh-select-image__img {
  width: 32px;
  height: 32px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 50%;
}

.select2-dropdown {
  border-radius: 0rem;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px 0px;
}

.select2-container--open .select2-dropdown--above {
  top: -10px;
}

.select2-container--open .select2-dropdown--below {
  top: 10px;
}

.select2-container--open .select2-dropdown--below,
.select2-container--open .select2-dropdown--above {
  border: 1px solid hsl(213, 22%, 93%);
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}

.select2-results__option--selected {
  background-color: hsl(213, 22%, 93%) !important;
}

.select2-results__option--highlighted {
  background-color: rgba(233.223, 236.7573, 241.077, 0.5) !important;
  color: hsl(0, 0%, 11%) !important;
}

.oh-select-no-search + .select2-container,
.oh-select-2 + .select2-container {
  width: 100% !important;
}

.oh-select-no-search + .select2-container .select2-selection--single,
.oh-select-2 + .select2-container .select2-selection.select2-selection--single {
  border: 1px solid hsl(213, 22%, 93%);
  height: 48.54px !important;
  display: flex;
  align-items: center;
}
.oh-select-no-search + .select2-container .select2-selection--single:focus, .oh-select-no-search + .select2-container .select2-selection--single:focus-visible, .oh-select-no-search + .select2-container .select2-selection--single:active,
.oh-select-2 + .select2-container .select2-selection.select2-selection--single:focus,
.oh-select-2 + .select2-container .select2-selection.select2-selection--single:focus-visible,
.oh-select-2 + .select2-container .select2-selection.select2-selection--single:active {
  border: 2px solid hsl(0, 0%, 13%) !important;
}

.select2-search__field {
  border-radius: 0px !important;
}

.select2-container--open .select2-selection {
  border-radius: 0rem !important;
}

.oh-select-no-search + .select2-container .select2-selection__arrow,
.select2-container--default .select2-selection--single .select2-selection__arrow {
  display: block;
  height: 100% !important;
}

.oh-select-no-search + .select2-container .select2-selection__rendered {
  height: 100%;
  display: flex !important;
  align-items: center !important;
}

.oh-select-image + .select2-container .select2-selection__rendered {
  height: unset !important;
}

.select2-results__option {
  padding: 0.4rem;
}

.select2-selection__arrow {
  height: 100%;
}

.oh-select:disabled + .select2-container,
.oh-select:disabled + .select2-container .select2-selection__rendered {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.oh-select:disabled + .select2-container .select2-selection {
  background-color: transparent !important;
  border: none !important;
}

.oh-select:disabled + .select2-container .select2-selection .select2-selection__arrow {
  display: none !important;
}

.oh-select-no-search + .select2-container .select2-selection--single {
  height: unset;
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}

.select2-container--default .select2-selection--single {
  border-radius: 0px !important;
  border-color: hsl(213, 22%, 84%) !important;
}

.oh-select-no-search + .select2-container .select2-selection--single {
  height: 48.84px;
}

.oh-select--sm + .select2-container .select2-selection--single {
  height: 38px !important;
}

.oh-select-no-search + .select2-container,
.select2-container--default .select2-selection--multiple {
  border-radius: 0px !important;
  border-color: hsl(213, 22%, 84%) !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: hsl(0, 0%, 100%) !important;
  background: hsl(0, 0%, 100%) !important;
  border-color: hsl(213, 22%, 84%) !important;
  border-radius: 0px !important;
}

/* =================================
*     CHECKBOXES AND SWITCHES
* ================================ */
.oh-switch {
  display: flex;
  align-items: center;
}

.oh-switch .oh-switch__checkbox {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #dfe1e4;
  border-radius: 72px;
  border-style: none;
  flex-shrink: 0;
  height: 20px;
  margin: 0;
  position: relative;
  width: 30px;
  display: inline-block;
  cursor: pointer;
}

.oh-switch .oh-switch__checkbox::before {
  bottom: -6px;
  content: "";
  left: -6px;
  position: absolute;
  right: -6px;
  top: -6px;
}

.oh-switch .oh-switch__checkbox,
.oh-switch .oh-switch__checkbox::after {
  transition: all 100ms ease-out;
}

.oh-select-2--large + .select2-container .select2-selection--multiple {
  height: 48.84px;
}

.oh-switch .oh-switch__checkbox::after {
  background-color: #fff;
  border-radius: 50%;
  content: "";
  height: 14px;
  left: 3px;
  position: absolute;
  top: 3px;
  width: 14px;
}

.oh-switch input[type=checkbox] {
  cursor: default;
}

.oh-switch .oh-switch__checkbox:hover {
  background-color: #c9cbcd;
  transition-duration: 0s;
}

.oh-switch .oh-switch__checkbox:checked {
  background-color: hsl(8, 77%, 56%);
}

.oh-switch .oh-switch__checkbox:checked::after {
  background-color: #fff;
  left: 13px;
}

.oh-switch :focus:not(.focus-visible) {
  outline: 0;
}

.oh-switch .oh-switch__checkbox:checked:hover {
  background-color: hsl(8, 77%, 56%);
}

.oh-password-input-container {
  position: relative;
}

.oh-password-input--toggle {
  position: absolute;
  top: 1px;
  right: 1px;
  height: 100%;
  font-size: 1.25rem;
}

/* ===============================
*     RADIO, ANSWERS & oh-rate
* ==============================*/
.oh-questions {
  padding-left: 0;
  list-style-type: none;
}

.oh-radio {
  display: block;
  position: relative;
  padding-left: 20px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.oh-radio input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  left: 0;
  top: 9px;
  z-index: 5;
}

.oh-radio__checkmark {
  position: absolute;
  top: 9px;
  left: 0;
  height: 16px;
  width: 16px;
  background-color: #eee;
  border-radius: 50%;
  z-index: initial;
}

.oh-radio:hover input ~ .oh-radio__checkmark {
  background-color: hsl(213, 22%, 93%);
}

.oh-radio input:checked ~ .oh-radio__checkmark {
  background-color: hsl(8, 77%, 56%);
}

.oh-radio__checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.oh-radio input:checked ~ .oh-radio__checkmark:after {
  display: block;
}

.oh-radio .oh-radio__checkmark:after {
  top: 4.5px;
  left: 4.5px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: white;
}

.oh-radio .oh-label {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
  cursor: pointer;
}

.oh-rate {
  display: inline-block;
  height: 36px;
  padding: 0;
}

.oh-rate:not(:checked) > input {
  position: absolute;
  top: -9999px;
}

.oh-rate:not(:checked) > label {
  float: right;
  width: 1.15em;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
  font-size: 1.25rem;
  color: #ccc;
}

.oh-rate:not(:checked) > label:before {
  content: "★ ";
}

.oh-rate > input:checked ~ label {
  color: #ffc700;
}

.oh-rate:not(:checked) > label:hover,
.oh-rate:not(:checked) > label:hover ~ label {
  color: #deb217;
}

.oh-rate > input:checked + label:hover,
.oh-rate > input:checked + label:hover ~ label,
.oh-rate > input:checked ~ label:hover,
.oh-rate > input:checked ~ label:hover ~ label,
.oh-rate > label:hover ~ input:checked ~ label {
  color: #c59b08;
}

/* ===============================
*     REGISTRATION - PHOTO
* ==============================*/
.oh-profile-photo {
  font-size: 2rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  border: 1px dashed hsl(0, 0%, 11%);
  border-radius: 50%;
  margin-bottom: 0.75rem;
  background: rgba(233.223, 236.7573, 241.077, 0.2);
  cursor: pointer;
}
.oh-profile-photo:hover {
  background: rgba(233.223, 236.7573, 241.077, 0.4);
}

.oh-profile-photo__input {
  display: none;
}

.oh-kanban {
  width: 100%;
  border-top: 1px solid hsl(213, 22%, 84%);
  display: flex;
  align-items: flex-start;
  min-height: calc(100vh - 180px);
  height: calc(100vh - 245px);
  padding-top: 0;
  overflow: auto;
}

.oh-kanban__section {
  width: 250px;
  margin-right: 1.5rem;
  flex-shrink: 0;
}
.oh-kanban__section:last-child {
  margin-right: 0;
}

.oh-kanban .ui-sortable-placeholder {
  min-height: calc(100vh - 245px);
}

.oh-kanban__add-container {
  padding-top: 0.5rem;
  top: 0;
  position: sticky;
}

.oh-kanban__section-title {
  font-size: 1rem;
  font-weight: bold;
}

.oh-kanban__section-head {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  margin-bottom: 0.5rem;
  background-color: hsl(0, 0%, 97.5%);
  position: sticky;
  min-height: 50px;
  top: 0;
  z-index: 3;
}

.oh-kanban__section-head--white {
  background-color: hsl(0, 0%, 100%) !important;
}

.oh-kanban__head-actions {
  display: flex;
  align-items: center;
}

.oh-kanban__btn {
  padding: 0;
  font-size: 1rem;
  margin-right: 0.5rem;
}
.oh-kanban__btn:last-child {
  margin-right: 0;
}

.oh-kanban__card {
  background-color: hsl(0, 0%, 100%);
  border: 1px solid hsl(213, 22%, 84%);
  border-bottom: none;
  width: 100%;
  flex-shrink: 0;
}
.oh-kanban__card:hover {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.02) 0px 1px 2px 0px;
}
.oh-kanban__card:last-child {
  border-bottom: 1px solid hsl(213, 22%, 84%);
}

.oh-kanban__card-head,
.oh-kanban__card-footer {
  padding: 0.75rem;
}

.oh-kanban__card-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.oh-kanban__card-body {
  position: relative;
  padding: 0 0.75rem;
}

.oh-kanban__card-footer-text {
  font-size: 0.8rem;
}

.oh-kanban__section-body {
  width: 100%;
  min-height: calc(100vh - 245px);
}

.oh-kanban__add-section {
  min-width: 225px;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid hsl(213, 22%, 93%);
}
.oh-kanban__add-section:hover {
  border: 1px solid hsl(213, 22%, 84%);
}

.oh-kanban__add-dialog,
.oh-kanban__add-card-container {
  padding: 0.75rem;
}

.oh-kanban__section--highlight {
  background-color: hsl(185, 68%, 98%);
  border: 1px dashed hsl(185, 68%, 42%);
  width: calc(250px + 1rem);
  height: calc(100vh - 180px);
}

.oh-kanban__card--highlight {
  background-color: hsl(185, 68%, 98%);
  border: 1px dashed hsl(185, 68%, 42%);
  width: 250px;
  height: 100px;
}

.oh-kanban-group-container {
  display: flex;
  align-items: flex-start;
  max-width: 100%;
  height: calc(100vh - 180px);
  overflow-x: auto;
}
.oh-kanban-group {
  min-width: 300px;
  max-width: calc(33.33% - 0.5rem);
  margin-right: 1rem;
  flex-shrink: 0;
}

.oh-kanban-group .oh-kanban-card {
  margin-bottom: 1rem;
}
.oh-kanban-group .oh-kanban-card:last-child {
  margin-bottom: 0;
}

.oh-kanban-group__head {
  position: relative;
  margin-bottom: 1.25rem;
  width: 100%;
  padding: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.oh-kanban-group__head:hover {
  background-color: hsl(213, 22%, 93%);
}

.oh-kanban-group__add-card {
  font-size: 1.25rem;
  padding: 0;
}

.oh-kanban-card--collapsed .oh-kanban-group__add-card {
  display: none;
}

.oh-kanban-gorup__title {
  font-weight: bold;
}

.oh-kanban-card--collapsed.oh-kanban-group {
  min-width: 30px;
}

.oh-kanban-card--collapsed .oh-kanban-group__head {
  background-color: hsl(213, 22%, 93%);
  position: relative;
  height: calc(100vh - 200px);
  width: 35px;
}
.oh-kanban-card--collapsed .oh-kanban-group__head::before {
  content: "";
  width: 18px;
  height: 18px;
  display: block;
  position: absolute;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzdkN2Q3ZCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBzdHJva2Utd2lkdGg9IjEuNSIgdmlld0JveD0iMCAwIDI0IDI0IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogIDxwYXRoIGQ9Ik01Ljk5NCAxNiAyIDEyLjAwMyA2IDgiPjwvcGF0aD4KICA8cGF0aCBkPSJNMTguMDA2IDggMjIgMTEuOTk3IDE4IDE2Ij48L3BhdGg+CiAgPHBhdGggZD0iTTIgMTJoMjAiPjwvcGF0aD4KPC9zdmc+");
  background-repeat: no-repeat;
  background-size: contain;
  bottom: 10px;
}
.oh-kanban-card--collapsed .oh-kanban-group__head:hover {
  background-color: hsl(213, 22%, 84%);
}

.oh-kanban-card--collapsed .oh-kanban-gorup__title {
  transform-origin: 0 0;
  transform: rotate(-90deg);
  position: absolute;
  width: calc(100vh - 260px);
  left: 0;
  bottom: 0px;
  left: 5px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.oh-kanban-card--collapsed .oh-kanban-group__body {
  display: none;
}

.oh-kanban__card--status {
  position: relative;
  margin-top: 1rem;
}
.oh-kanban__card--status::before {
  content: "";
  width: 100%;
  height: 4px;
  position: absolute;
  left: 0px;
  bottom: 0px;
}
.oh-kanban__card--status:first-child {
  margin-top: 0;
}

.oh-kanban__card--blue::before {
  content: "";
  background-color: hsl(225, 72%, 48%);
}

.oh-kanban__card--red::before {
  content: "";
  background-color: hsl(0, 71%, 54%);
}

.oh-kanban__card--amber::before {
  content: "";
  background-color: hsl(44, 89%, 62%);
}

.oh-kanban__card--green::before {
  content: "";
  background-color: hsl(148, 70%, 40%);
}

.oh-kanban__card-body-collapse {
  width: 24px;
  height: 24px;
  display: inline-block;
  border: none;
  background-color: hsl(0, 0%, 97.5%);
  border-radius: 5px;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIGZpbGw9IiM2OTY5NjkiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJtNyAxNC41IDUtNSA1IDVIN1oiPjwvcGF0aD4KPC9zdmc+");
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  right: 10px;
}
.oh-kanban__card-body-collapse:hover {
  background-color: hsl(0, 0%, 96%);
}

.oh-kanban__card-collapse--down {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIGZpbGw9IiM2OTY5NjkiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJtNyA5LjUgNSA1IDUtNUg3WiI+PC9wYXRoPgo8L3N2Zz4=") !important;
}

.oh-kanban__card-content--hide {
  display: none;
}

.oh-modal {
  position: fixed;
  background-color: rgba(28.05, 28.05, 28.05, 0.2);
  z-index: 1021;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  display: none;
  transition: all 0.4 ease-in-out;
  animation: fade-in-animation 0.2s 0s ease-in-out forwards;
}
@keyframes fade-in-animation {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@media (max-width: 575.98px) {
  .oh-modal {
    padding-left: 0.7rem;
    padding-right: 0.7rem;
  }
}

.oh-modal--show {
  display: block;
  transition: all 0.4 ease-in-out;
}

.oh-modal__dialog {
  background-color: hsl(0, 0%, 100%);
  max-width: 650px;
  border-radius: 0rem;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 12px 28px 0px, rgba(0, 0, 0, 0.1) 0px 2px 4px 0px, rgba(255, 255, 255, 0.05) 0px 0px 0px 1px inset;
  margin: 1.75rem auto;
}

.oh-modal__dialog-header {
  position: relative;
  padding: 1.75rem 1.75rem 0.5rem;
  width: 100%;
}

.oh-modal__dialog-body {
  padding: 0 1.75rem 1.75rem 1.75rem;
}

.oh-modal__dialog-title {
  font-size: 1.05rem;
}

.oh-modal__close {
  border: none;
  background: none;
  font-size: 1.5rem;
  opacity: 0.7;
  position: absolute;
  top: 25px;
  right: 15px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
.oh-modal__close:hover {
  opacity: 1;
  transition: all 0.3s ease-in-out;
}

.oh-modal__section-head {
  border-bottom: 1px solid hsl(0, 0%, 95%);
  padding-bottom: 0.75rem;
  color: hsl(0, 0%, 37%);
  margin-bottom: 0.75rem;
}

.oh-modal__dialog-footer {
  padding: 0.25rem 1.75rem 1.75rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.oh-modal__dialog--timeoff {
  max-width: 350px;
}

.oh-modal__label {
  display: block;
  font-size: 0.8rem;
  color: hsl(0, 0%, 45%);
  margin-bottom: 0.25rem;
}

.oh-modal__value {
  font-size: 1.05rem;
  font-weight: bold;
}

.oh-modal__description {
  max-height: 150px;
  overflow-y: auto;
}

.oh-modal__dialog--confirm {
  max-width: 350px;
}

.oh-modal__dialog--confirm .oh-modal__dialog-title {
  font-weight: bold;
}

.oh-modal__dialog--confirm .oh-modal__dialog-footer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.oh-pagination {
  margin-top: 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 575.98px) {
  .oh-pagination {
    flex-direction: column;
    align-items: flex-start;
  }
}

.oh-pagination__page {
  color: hsl(0, 0%, 11%);
  font-size: 0.9rem;
}

.oh-pagination__items {
  display: flex;
  align-items: center;
  list-style-type: none;
  padding-left: 0;
}

.oh-pagination__item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  padding: 10px;
  background-color: hsl(0, 0%, 100%);
  border: 1px solid hsl(213, 22%, 84%);
  border-right: 0px;
  font-size: 0.9rem;
  cursor: pointer;
}
.oh-pagination__item:first-child {
  border-radius: 0;
}
.oh-pagination__item:last-child {
  border-right: 1px solid hsl(213, 22%, 84%);
  border-radius: 0;
}
.oh-pagination__item:hover {
  background-color: hsl(0, 0%, 95%);
}

.oh-pagination__item--wide {
  width: -moz-fit-content;
  width: fit-content;
}

.oh-pagination__link:hover {
  text-decoration: none;
}

.oh-pagination__link--active {
  color: hsl(8, 77%, 56%);
}

.oh-pagination__nav {
  display: flex;
}

.oh-pagination__input-container {
  margin-right: 1rem;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
@media (max-width: 575.98px) {
  .oh-pagination__input-container {
    margin-left: 0;
  }
}

@media (max-width: 575.98px) {
  .oh-pagination__input-container .oh-pagination__label:first-child {
    display: none;
  }
}

.oh-pagination__input {
  width: 40px;
  height: 40px;
  border: 1px solid hsl(213, 22%, 84%);
  text-align: center;
  border-radius: 0px;
}
.oh-pagination__input:hover, .oh-pagination__input:focus, .oh-pagination__input:focus-visible {
  border: 2px solid hsl(0, 0%, 13%);
  border-radius: 0;
}

.oh-pagination__label {
  color: hsl(0, 0%, 11%);
  font-size: 0.85rem;
}

.oh-pagination__page {
  margin-bottom: 0.75rem;
}

.oh-profile {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.oh-profile__avatar {
  border-radius: 50%;
  flex-shrink: 0;
}

.oh-profile__image {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
  border-radius: 50%;
}

.oh-profile__active-badge {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  position: absolute;
  right: 5px;
  bottom: 5px;
  cursor: pointer;
}

.oh-profile__active-badge--secondary {
  background-color: hsl(8, 77%, 56%);
  border: 2px solid hsl(0, 0%, 100%);
}

.oh-profile--md .oh-profile__avatar {
  width: 30px;
  height: 30px;
}
.oh-profile--md .oh-profile__name {
  font-size: 0.8rem;
}

.oh-profile--base .oh-profile__avatar {
  width: 42px;
  height: 42px;
}
.oh-profile--base .oh-profile__name {
  font-size: 0.95rem;
}

.oh-profile--lg .oh-profile__avatar {
  width: 90px;
  height: 90px;
}
.oh-profile--lg .oh-profile__name {
  font-size: 0.95rem;
}

.oh-profile-list {
  list-style-type: none;
  padding-left: 0;
  margin-bottom: 0;
}

.oh-profile-list__item {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.oh-profile-list__item:last-child {
  padding-bottom: 0;
}

.oh-profile-list__link:hover {
  text-decoration: none;
  opacity: 0.9;
}

.oh-profile__info-name {
  font-size: 1.4rem;
}
@media (max-width: 575.98px) {
  .oh-profile__info-name {
    font-weight: bold;
    font-size: 1.2rem;
  }
}

.oh-profile__info-designation {
  margin-bottom: 0;
  color: hsl(0, 0%, 45%);
}

.oh-profile__info-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.oh-profile__info-list {
  margin-bottom: 0;
  padding-left: 0;
}
@media (max-width: 767.98px) {
  .oh-profile__info-list {
    margin-top: 2rem;
  }
}

.oh-profile__info-list-item {
  display: flex;
  align-items: center;
}

.oh-profile__info-label, .oh-profile__info-icon {
  color: hsl(216, 3%, 39%);
  font-size: 0.85rem;
}

.oh-profile__info-label {
  display: flex;
  align-items: center;
  margin-right: 0.75rem;
}

.oh-profile__info-label > *:first-child {
  margin-right: 0.35rem;
}

.oh-profile__info-value {
  font-size: 0.9rem;
}

.oh-profile__info-list-item {
  margin-bottom: 0.75rem;
}
.oh-profile__info-list-item:last-child {
  margin-bottom: 0;
}

.oh-general__tabs--border {
  padding-bottom: 0.8rem;
  border-bottom: 1px solid hsl(213, 22%, 93%);
}

.oh-profile__card-info-list {
  padding-left: 0;
  list-style-type: none;
}

.oh-profile__card-info-item {
  display: flex;
  flex-direction: column;
}

.oh-profile__card-info-item {
  margin-bottom: 1.5rem;
}
.oh-profile__card-info-item:last-child {
  margin-bottom: 0;
}

.oh-profile__subtext {
  font-size: 0.8rem;
  color: hsl(0, 0%, 45%);
  margin-bottom: 0;
}

.oh-profile__meta {
  margin-left: 0.3rem;
}

.oh-progress {
  background-color: hsl(0, 0%, 96%);
  border-radius: 25px;
  width: 100%;
  height: 8px;
  display: block;
}

.oh-progress__bar {
  display: block;
  height: 100%;
  border-radius: 25px;
}

.oh-progress__bar--secondary, .oh-progress__bar--table-secondary {
  background-color: hsl(8, 77%, 56%);
}

.oh-progress--table-secondary {
  border: 1px solid hsl(8, 77%, 56%);
}

.oh-progress__bar--table {
  border-radius: 2px;
  max-width: 80%;
}

.oh-progress--table {
  height: 25px;
  border-radius: 5px;
  background-color: transparent;
  width: 75%;
}

.oh-progress-container {
  display: flex;
  align-items: center;
}

.oh-progress-container__percentage {
  font-size: 0.8rem;
  font-weight: bold;
  margin-left: 0.7rem;
  color: hsl(0, 0%, 45%);
}

.oh-sidebar {
  background-color: hsl(0, 0%, 13%);
  color: hsl(0, 0%, 100%);
  max-width: 230px;
  min-height: 100vh;
  height: 100%;
  overflow-y: auto;
  left: 0;
  z-index: 99;
}

.oh-sidebar__company {
  display: grid;
  grid-template-columns: 1fr 4fr auto;
  align-items: center;
  padding: 25px 20px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.15);
  width: 230px;
}
.oh-sidebar__company div:first-child {
  margin-right: 10px;
}

.oh-sidebar__company-profile {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: hsl(8, 77%, 56%);
  font-size: 1.25rem;
  border-radius: 0.15rem;
}

.oh-sidebar__company-details {
  display: flex;
  flex-direction: column;
}

.oh-sidebar__company-title {
  color: hsl(0, 0%, 100%);
  font-size: 0.95rem;
}

.oh-sidebar__company-link {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.7);
}
.oh-sidebar__company-link:hover {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
}
.oh-sidebar__company-link:active, .oh-sidebar__company-link:focus, .oh-sidebar__company-link:focus-visible {
  color: hsl(0, 0%, 100%);
  text-decoration: none;
}

.oh-sidebar__menu-items {
  padding-left: 0px;
  margin-top: 25px;
  list-style: none;
}

.oh-sidebar__menu-item {
  font-size: 0.8rem;
}

.oh-sidebar__menu-link {
  display: grid;
  grid-template-columns: auto 1fr;
  color: hsl(0, 0%, 100%);
  opacity: 1;
  padding-left: 20px;
  padding-right: 20px;
  padding: 0.85rem;
  border-radius: 5px;
  margin-left: 20px;
  margin-right: 20px;
  width: auto;
}
.oh-sidebar__menu-link:hover {
  color: hsl(0, 0%, 100%);
  text-decoration: none;
  opacity: 0.7;
}
.oh-sidebar__menu-link:active, .oh-sidebar__menu-link:focus, .oh-sidebar__menu-link:focus-visible {
  color: hsl(0, 0%, 100%);
  text-decoration: none;
}

.oh-sidebar__menu-link--active {
  color: hsl(0, 0%, 100%);
  background-color: hsl(0, 0%, 20%);
}

.oh-sidebar__menu-icon {
  margin-right: 10px;
}

.oh-sidebar__submenu {
  padding: 17px 25px;
  background-color: hsl(0, 0%, 13%);
  width: 230px;
}

.oh-sidebar__submenu-items {
  padding-left: 0;
  list-style-type: none;
}

.oh-sidebar__submenu-item {
  font-size: 0.85rem;
  padding-left: 15px;
  margin-top: 15px;
  margin-bottom: 15px;
}
.oh-sidebar__submenu-item:first-child {
  margin-top: 0;
}
.oh-sidebar__submenu-item:last-child {
  margin-bottom: 0;
}

.oh-sidebar__submenu-link {
  color: rgba(255, 255, 255, 0.7);
}
.oh-sidebar__submenu-link:hover, .oh-sidebar__submenu-link:focus, .oh-sidebar__submenu-link:focus-visible, .oh-sidebar__submenu-link:visited {
  color: white;
  text-decoration: none;
}

.oh-sidebar__transition {
  transition: all 0.3s ease-in-out;
}

.oh-sidebar__animation--enter-start {
  transform: translateX(-100%);
}

.oh-sidebar__animation--enter-end {
  transform: translateX(0%);
}

.oh-sidebar__animation--leave-start {
  transform: translateX(0%);
}

.oh-sidebar__animation--leave-end {
  transform: translateX(-100%);
}

/* ===================================
*         Activity Sidebar
* ================================== */
.oh-activity-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  background-color: hsl(0, 0%, 100%);
  height: 100%;
  min-height: 100vh;
  width: 50%;
  max-width: 1000px;
  box-shadow: rgba(0, 0, 0, 0.1) -4px 9px 25px -6px;
  padding: 2rem;
  display: none;
  z-index: 15;
}
@media (max-width: 767.98px) {
  .oh-activity-sidebar {
    width: 95%;
  }
}

.oh-activity-sidebar--show {
  display: block;
  animation: slide-right-in-animation 0.4s 0s ease-in-out forwards;
}
@keyframes slide-right-in-animation {
  from {
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}

.oh-activity-sidebar__title,
.oh-activity-sidebar__header-icon {
  font-size: 1.25rem;
}

.oh-activity-sidebar__header {
  display: flex;
  align-items: center;
  margin-bottom: 1.05rem;
}

.oh-activity-sidebar__body {
  overflow-y: auto;
  min-height: calc(100% - 90px);
  max-height: calc(100% - 90px);
}

.oh-activity-sidebar__qa-list {
  padding-left: 0;
  list-style: none;
}

.oh-activity-sidebar__qa-item {
  background-color: hsl(0, 0%, 100%);
  padding: 1.25rem;
  border-radius: 0.5rem;
  border: 1px solid hsl(213, 22%, 93%);
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
  transition: background-color 0.4s ease-in-out;
  margin: 1rem auto;
}
.oh-activity-sidebar__qa-item:last-child {
  margin-bottom: 0rem;
}
.oh-activity-sidebar__qa-item:hover {
  background-color: rgba(233.223, 236.7573, 241.077, 0.4);
  transition: background-color 0.4s ease-in-out;
  cursor: pointer;
}

.oh-activity-sidebar__q {
  display: block;
  font-weight: bold;
}

.oh-activity-sidebar__a {
  display: block;
  color: hsl(0, 0%, 45%);
  margin-top: 0.35rem;
}

span.oh-activity-sidebar__a img {
  height: unset !important;
}

.oh-activity-sidebar__close:hover {
  cursor: pointer;
  opacity: 0.6;
  transition: all 0.4s ease-in-out;
}

/* ===================================
*         Inner Sidebar
* ================================== */
.oh-inner-sidebar {
  width: 100%;
  border-right: 1px solid hsl(213, 22%, 93%);
  padding: 1.5rem 1rem;
}
@media (max-width: 991.98px) {
  .oh-inner-sidebar {
    border: none;
  }
}

.oh-inner-sidebar-toggle {
  display: none;
  background-color: transparent;
  border-bottom: 1px solid hsl(213, 22%, 93%);
  margin-bottom: 1.5rem;
  padding-top: 0;
}
.oh-inner-sidebar-toggle:hover, .oh-inner-sidebar-toggle:focus, .oh-inner-sidebar-toggle:focus-visible {
  background-color: transparent;
  color: hsl(8, 61%, 50%);
  border-bottom: 1px solid hsl(213, 22%, 93%);
}
@media (max-width: 991.98px) {
  .oh-inner-sidebar-toggle {
    display: flex;
  }
}

.oh-inner-sidebar__items {
  list-style-type: none;
  padding-left: 0;
  max-height: 100%;
  overflow-y: auto;
}

.oh-inner-sidebar__item {
  margin-bottom: 0.05rem;
}
.oh-inner-sidebar__item:last-child {
  margin-bottom: 0;
}

.oh-inner-sidebar__link {
  font-size: 0.88rem;
  display: block;
  padding: 0.7rem;
  width: 100%;
}
.oh-inner-sidebar__link:hover, .oh-inner-sidebar__link:focus, .oh-inner-sidebar__link:focus-visible {
  color: hsl(8, 77%, 56%);
  text-decoration: none;
}

.oh-inner-sidebar__link--active {
  background-color: #fff6f4;
  color: hsl(8, 77%, 56%);
  border: solid 1px;
  border-radius: 5px;
}

.oh-inner-sidebar-content {
  width: 100%;
  padding: 1.5rem 0;
}

.oh-inner-sidebar-content__header {
  padding-bottom: 0.5rem;
  border-bottom: 1px solid hsl(213, 22%, 93%);
  margin-bottom: 1rem;
}

.oh-inner-sidebar-content__title {
  font-size: 1.25rem;
  font-weight: bold;
}

.oh-inner-sidebar-content__footer {
  padding-top: 1rem;
  border-top: 1px solid hsl(213, 22%, 93%);
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}

/* =================================
*        COLLAPSED SIDEBAR
* ================================ */
@media (max-width: 767.98px) {
  .oh-wrapper-main--closed #sidebar {
    display: none;
  }
}

.oh-wrapper-main--closed .oh-sidebar__company-details {
  display: none;
}
.oh-wrapper-main--closed .oh-sidebar__company {
  display: flex;
  align-items: center;
  justify-content: center;
  width: unset;
}
.oh-wrapper-main--closed .oh-sidebar__company .oh-dropdown {
  display: none;
}
.oh-wrapper-main--closed .oh-sidebar__company .oh-sidebar__company-profile {
  margin-right: 0;
}
.oh-wrapper-main--closed .oh-sidebar__submenu-items,
.oh-wrapper-main--closed .oh-sidebar__menu-link span {
  display: none;
}
.oh-wrapper-main--closed .oh-sidebar__menu-item {
  margin-bottom: 1rem;
}
.oh-wrapper-main--closed .oh-sidebar__menu-item:last-child {
  margin-bottom: 0;
}
.oh-wrapper-main--closed .oh-sidebar__submenu {
  position: fixed;
  z-index: -1;
  pointer-events: none;
}
.oh-wrapper-main--closed .oh-sidebar__menu-link {
  display: flex;
  justify-content: center;
  align-items: center;
}
.oh-wrapper-main--closed .oh-sidebar__menu-icon {
  margin-right: 0;
}
.oh-wrapper-main--closed .oh-sidebar__menu-icon img {
  height: 24px;
  width: 24px;
}
.oh-wrapper-main--closed .oh-sidebar__menu-link,
.oh-wrapper-main--closed .oh-sidebar__submenu {
  width: unset;
}

.oh-table {
  width: 100%;
  font-size: 0.85rem;
  border-collapse: collapse;
  border: 1px solid hsl(0, 0%, 95%);
  background-color: hsl(0, 0%, 100%);
}
@media (max-width: 991.98px) {
  .oh-table {
    display: block;
    max-width: 100vw;
    overflow-x: auto;
  }
}

.oh-table thead tr th {
  padding: 1.25rem 0.65rem;
  text-align: left;
  border-bottom: 3px solid hsl(0, 0%, 95%);
  color: hsl(0, 0%, 37%);
  font-weight: 400;
}

.oh-table tbody tr td {
  padding: 1.25rem 0.65rem;
  border-bottom: 1px solid hsl(0, 0%, 95%);
}

.oh-table tbody tr:hover {
  background-color: hsl(0, 0%, 96%);
  cursor: pointer;
}

.oh-table tbody {
  max-height: 400px;
  overflow-y: auto;
}

/* ====================================
*        Sticky Table
* ================================== */
.oh-sticky-table {
  max-height: 75vh;
  overflow: auto;
  margin: auto;
  max-width: 100%;
  border: 1px solid hsl(213, 22%, 93%);
  position: relative;
}

.oh-sticky-table--no-highlight .oh-sticky-table__tr:hover {
  background-color: transparent;
}

.oh-sticky-table--no-overflow {
  overflow-x: auto;
  overflow-y: visible;
}

.oh-sticky-table__table {
  display: table;
  position: relative;
  width: 100%;
  table-layout: fixed;
}

.oh-sticky-table__tr {
  display: table-row;
}
.oh-sticky-table__tr:hover {
  background-color: hsl(0, 0%, 96%);
  cursor: pointer;
}

.oh-sticky-table__sd {
  background-color: hsl(0, 0%, 100%);
  z-index: 10 !important;
  width: 240px;
}

.oh-sticky-table__th:first-child {
  z-index: 11 !important;
}

.oh-sticky-table__th {
  z-index: 10;
  background-color: hsl(0, 0%, 100%);
  padding: 0.5rem 1rem !important;
}

.oh-sticky-table__thead {
  display: table-header-group;
  font-weight: bold;
  z-index: 1;
}

.oh-sticky-table__tbody {
  display: table-row-group;
  height: 60px;
}

.oh-sticky-table__tbody--highlight {
  height: 60px;
  width: 100vw;
  background-color: hsl(185, 68%, 98%);
  border: 1px dashed hsl(185, 68%, 42%);
  border-left: none;
  border-right: 0;
}

.oh-sticky-table__th,
.oh-sticky-table__sd {
  position: sticky;
  background: hsl(0, 0%, 100%);
  color: hsl(0, 0%, 11%);
}

.oh-sticky-table__th {
  top: 0;
  border-bottom: 2px solid hsl(213, 22%, 93%);
}

.oh-sticky-table__sd {
  left: 0;
  border-right: 1px solid hsl(213, 22%, 93%);
}
@media (max-width: 575.98px) {
  .oh-sticky-table__sd {
    position: relative;
  }
}

.oh-sticky-table__sd,
.oh-sticky-table__td,
.oh-sticky-table__th {
  padding: 0.6rem;
}

.oh-sticky-table--fit .oh-sticky-table__sd,
.oh-sticky-table--fit .oh-sticky-table__td,
.oh-sticky-table--fit .oh-sticky-table__th {
  padding: 0.6rem;
  width: -moz-fit-content;
  width: fit-content;
}

.oh-sticky-table__thead div.oh-sticky-table__th:first-child,
.oh-sticky-table__tfoot div.oh-sticky-table__th:first-child {
  left: 0;
  z-index: 1;
  border-right: 1px solid hsl(213, 22%, 93%);
}
@media (max-width: 575.98px) {
  .oh-sticky-table__thead div.oh-sticky-table__th:first-child,
  .oh-sticky-table__tfoot div.oh-sticky-table__th:first-child {
    position: relative;
  }
}

.oh-sticky-table__sr {
  position: sticky;
  right: 0;
  background-color: hsl(0, 0%, 100%);
  z-index: 9 !important;
  padding: 1rem;
  border-bottom: 1px solid hsl(213, 22%, 93%);
  border-left: 1px solid hsl(213, 22%, 93%);
  display: table-cell;
}

.oh-sticky-table__th--sticky {
  right: 0;
  position: sticky;
  border-left: 1px solid hsl(213, 22%, 93%);
}

.oh-sticky-table__th--right {
  right: 0;
  border-left: 1px solid hsl(213, 22%, 93%);
}

.oh-sticky-table__button-container {
  display: flex;
  align-items: center;
}

.oh-sticky-table__button-container > * {
  flex-shrink: 0;
}

/* ========================================
*            EDITABLE INPUT
* ====================================== */
.oh-table__editable-input:-moz-read-only {
  color: hsl(0, 0%, 11%);
  background-color: transparent;
  border: none;
  height: 100%;
  border: 1px solid transparent;
}
.oh-table__editable-input:read-only {
  color: hsl(0, 0%, 11%);
  background-color: transparent;
  border: none;
  height: 100%;
  border: 1px solid transparent;
}
.oh-table__editable-input:-moz-read-only:hover {
  border: 1px solid hsl(213, 22%, 84%);
}
.oh-table__editable-input:read-only:hover {
  border: 1px solid hsl(213, 22%, 84%);
}
.oh-table__editable-input:-moz-read-only:focus, .oh-table__editable-input:-moz-read-only:focus-visible {
  outline: none;
  border: none;
}
.oh-table__editable-input:read-only:focus, .oh-table__editable-input:read-only:focus-visible {
  outline: none;
  border: none;
}

.oh-table__editable-input {
  border-radius: 0px;
  border: 1px solid hsl(0, 0%, 13%);
}
.oh-table__editable-input:focus, .oh-table__editable-input:focus-visible {
  outline: hsl(0, 0%, 13%) solid 2px;
  border-radius: 0px;
  border: none;
}

.oh-table__add-column {
  min-height: 55px;
}

.oh-table__add-row {
  position: sticky;
  left: 0;
}

.oh-table__add-row-dropdown {
  bottom: 0;
  transform: translateX(50%);
}
@media (max-width: 575.98px) {
  .oh-table__add-row-dropdown {
    transform: translateX(0);
  }
}

.oh-table-config__close-tr,
.oh-table-config__close-th {
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: hsl(213, 22%, 93%);
  position: absolute;
  right: 3px;
  top: 3px;
  display: none;
}

.oh-table-config__tr:not(:only-child):hover .oh-table-config__close-tr {
  display: flex;
}

.oh-table-config__th:not(:only-child):hover .oh-table-config__close-th {
  display: flex;
}

:is(.oh-table-config__th:nth-last-child(2)):is(.oh-table-config__th:nth-child(2)) .oh-table-config__close-th {
  display: none !important;
}

/* ========================================
*          COLLAPSABLE TABLE
* ====================================== */
.oh-table__toggle-button {
  padding: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
.oh-table__toggle-button:after {
  content: "";
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9IiMxZjFmMWYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMTkgMTNoLTZ2NmgtMnYtNkg1di0yaDZWNWgydjZoNnYyWiI+PC9wYXRoPgo8L3N2Zz4=");
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.oh-table__toggle-button.oh-table__toggle-button--show:after {
  content: "";
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9IiMxZjFmMWYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNNiAxMWgxMnYySDZ2LTJaIj48L3BhdGg+Cjwvc3ZnPg==");
  display: flex;
  align-items: center;
  justify-content: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.oh-table__toggle-child {
  display: none;
}

.oh-table__toggle-child.oh-table__toggle-child--show {
  display: table-row;
}

/* ========================================
*          TABLE CONTROLS
* ====================================== */
.oh-table-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  margin-bottom: 1rem;
}

/*..............Table sticky dropdown...................*/
.oh-sticky-dropdown_btn {
  font-weight: bold;
  background: hsl(0, 0%, 100%);
  color: hsl(0, 0%, 11%);
  border: transparent;
  width: 100%;
  padding: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.oh-sticky-table_dropdown {
  right: 0;
  width: -moz-max-content;
  width: max-content;
}

.oh-sticy-dropdown-item {
  font-weight: 400;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.oh-table__checkbox {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.oh-dropdown_btn-header {
  display: flex;
  align-items: center;
  margin: 0.8rem 0;
  gap: 0.4rem;
}

.oh-sticky-dropdown--header {
  position: absolute;
  z-index: 11;
  right: 0px;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 0px;
  border-top: 1px solid hsl(213, 22%, 93%);
}

.oh-table_sticky--wrapper {
  position: relative;
}

.oh-main__more-option-toggle {
  display: none;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 0.3rem;
  border: 1px solid hsl(213, 22%, 84%);
}
@media (max-width: 1000px) {
  .oh-main__more-option-toggle {
    display: flex;
  }
}

.oh-main_mob-toggler {
  display: flex;
  align-items: center;
  gap: 0.4rem;
}

.oh-titebar-button_mob--container {
  position: relative;
}
@media (max-width: 1000px) {
  .oh-titebar-button_mob--container {
    position: absolute;
    min-width: 300px;
    right: 10px;
    padding: 15px;
    background: white;
    z-index: 99;
    right: 16px;
    top: 160px;
    border-radius: 0rem;
    flex-direction: column;
  }
  .oh-titebar-button_mob--container button {
    width: 100%;
  }
  .oh-titebar-button_mob--container .oh-btn-group {
    width: 100%;
  }
  .oh-titebar-button_mob--container .oh-dropdown {
    width: 100%;
  }
}

@media (max-width: 1000px) {
  .oh-main__titlebar .oh-main__titlebar-button-container {
    display: none;
  }
}

.oh-main__titlebar .show {
  display: flex;
}

@media (max-width: 1000px) {
  .oh-main__titlebar-button-container > :first-child {
    margin-left: 0px;
  }
}

.oh-sticky-table__td,
.oh-sticky-table__th,
.oh-sticky-table__sd {
  display: table-cell;
  padding: 8px 10px;
  border-bottom: 0.2px solid hsl(213, 22%, 93%);
  border-right: 0.2px solid #fff;
  vertical-align: middle;
  width: 160px;
  word-break: break-word;
}

.oh-hover-btn {
  border: 2px solid #1c1c1c;
  background-color: transparent;
  border-radius: 3px;
  padding: 0.15rem 0.4rem;
}

.oh-hover-btn:hover {
  background-color: hsl(213, 22%, 93%);
}

.oh-hover-btn-container {
  position: relative;
  display: inline-block;
}

.oh-hover-btn-container:before {
  content: "";
  display: block;
  background-color: transparent;
  position: absolute;
  width: 100%;
  height: 20px;
  bottom: 100%;
}

.oh-hover-btn__small {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: hsl(0, 0%, 100%);
  border: 1px solid hsl(213, 22%, 93%);
  box-shadow: rgba(17, 17, 26, 0.05) 0px 4px 16px, rgba(17, 17, 26, 0.05) 0px 8px 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s linear;
}

.oh-hover-btn__small:hover {
  background-color: hsl(8, 77%, 56%);
  border: 1px solid hsl(8, 61%, 50%);
  color: hsl(0, 0%, 100%);
  transition: all 0.2s linear;
  transform: scale(105%);
}

.oh-hover-btn-drawer {
  align-items: center;
  position: absolute;
  display: flex;
  gap: 5px;
  z-index: 99;
  right: -20px;
  bottom: 130%;
  visibility: hidden;
  pointer-events: none;
  opacity: 0;
}

.oh-hover-btn-container:hover .oh-hover-btn-drawer {
  display: flex;
  visibility: visible;
  pointer-events: all;
  opacity: 1;
}

.table-wrapper {
  position: relative;
  overflow: scroll;
  max-height: 250px;
  border: 1px solid hsl(213, 22%, 93%);
}

.fixed-table {
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed;
}

.fixed-table th,
.fixed-table td {
  padding: 8px 10px;
  display: table-cell;
  height: 10px;
  line-height: 10px;
  text-align: left;
  vertical-align: middle;
  border-bottom: 0.2px solid hsl(213, 22%, 93%);
  border-right: 0.2px solid #fff;
  vertical-align: middle;
  width: 160px;
  /* word-break: break-word; */
}

.fixed-table td {
  font-size: 0.8rem;
  height: 45px;
  color: hsl(0, 0%, 11%);
  display: table-cell;
  /* padding: 8px 10px; */
  border-bottom: 0.2px solid hsl(213, 22%, 93%);
  border-right: 0.2px solid #fff;
  vertical-align: middle;
  width: 160px;
  /* word-break: break-word; */
}

.lastTd {
  background-color: white !important;
}

.fixed-table th:nth-child(2),
.fixed-table td:nth-child(2) {
  position: sticky;
  left: 30px;
  background-color: #f1f1f1;
  z-index: 2;
  width: 100px !important;
  background-color: #ffffff !important;
  border-right: 1px solid hsl(213, 22%, 93%) !important;
}

.fixed-table th:first-child,
.fixed-table td:first-child {
  position: sticky;
  left: 0;
  background-color: #f1f1f1;
  z-index: 2;
  width: 30px !important;
  background-color: #ffffff !important;
  border-right: 1px solid hsl(213, 22%, 93%) !important;
  /* To be on top of scrollable area */
}

.fixed-table thead tr {
  position: sticky;
  top: 0;
  z-index: 3;
  overflow: scroll;
}

.fixed-table thead th {
  border: transparent !important;
  border-bottom: 2px solid hsl(213, 22%, 93%) !important;
  background-color: #fff;
  height: 30px !important;
  font-size: 12px;
  color: hsl(0, 0%, 11%);
}

.table-wrapper {
  position: relative;
  overflow: scroll;
  max-height: 500px;
  border: 1px solid hsl(213, 22%, 93%);
}

.fixed-table {
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed;
}

.fixed-table th,
.fixed-table td {
  padding: 8px 10px;
  display: table-cell;
  height: 10px;
  line-height: 10px;
  text-align: left;
  vertical-align: middle;
  border-bottom: 0.2px solid hsl(213, 22%, 93%);
  border-right: 0.2px solid #fff;
  vertical-align: middle;
  width: 160px;
  word-break: break-word;
}

.fixed-table td {
  font-size: 0.8rem;
  height: 45px;
  color: hsl(0, 0%, 11%);
  display: table-cell;
  /* padding: 8px 10px; */
  border-bottom: 0.2px solid hsl(213, 22%, 93%);
  border-right: 0.2px solid #fff;
  vertical-align: middle;
  width: 160px;
  /* word-break: break-word; */
}

.lastTd {
  background-color: white !important;
}

.fixed-table th:nth-child(2),
.fixed-table td:nth-child(2) {
  position: sticky;
  left: 30px;
  background-color: #f1f1f1;
  z-index: 2;
  width: 100px !important;
  background-color: #ffffff !important;
  border-right: 1px solid hsl(213, 22%, 93%) !important;
}
@media (max-width: 768px) {
  .fixed-table th:nth-child(2),
  .fixed-table td:nth-child(2) {
    position: unset;
  }
}

.fixed-table th:first-child,
.fixed-table td:first-child {
  position: sticky;
  left: 0;
  background-color: #f1f1f1;
  z-index: 2;
  width: 30px !important;
  background-color: #ffffff !important;
  border-right: 1px solid hsl(213, 22%, 93%) !important;
  /* To be on top of scrollable area */
}

.fixed-table thead tr {
  position: sticky;
  top: 0;
  z-index: 3;
  overflow: scroll;
  box-shadow: 1px 1px hsl(213, 22%, 93%);
}

.fixed-table thead th {
  /* border-bottom: 2px solid hsl(213, 22%, 93%) !important; */
  background-color: #fff;
  height: 30px !important;
  font-size: 12px;
  color: hsl(0, 0%, 11%);
}

.add_c ul {
  display: flex;
  list-style: none;
  padding-left: 0;
  margin-bottom: 0px;
  justify-content: space-between;
}
.add_c ul li {
  padding: 5px 5px;
  flex-grow: 1;
}
.add_c ul li a {
  border: 1px solid hsl(213, 22%, 93%);
  padding: 3px 3px;
  display: block;
  word-break: auto-phrase;
}

.table-wrapper {
  scrollbar-width: thin;
  scrollbar-color: lightgrey #f1f1f1;
}

.table-wrapper::-webkit-scrollbar {
  width: 0;
  opacity: 0;
  -webkit-transition: opacity 0.3s ease;
  transition: opacity 0.3s ease;
  scrollbar-width: thin;
  display: none !important;
}

.table-wrapper:hover {
  scrollbar-color: lightgrey;
  transition: opacity 0.3s ease;
}

@keyframes shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}
.oh-table.is-loading tbody tr td,
.oh-table.is-loading thead tr th {
  position: relative;
}
.oh-table.is-loading tbody tr td::before,
.oh-table.is-loading thead tr th::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: -webkit-fill-available;
  height: 36%;
  border-radius: 6px;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
  margin-left: 5px;
  margin-right: 5px;
}

.fixed-table.is-loading tbody tr td,
.fixed-table.is-loading thead tr th {
  position: relative;
}
.fixed-table.is-loading tbody tr td::before,
.fixed-table.is-loading thead tr th::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: -webkit-fill-available;
  height: 36%;
  border-radius: 6px;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
  margin-left: 5px;
  margin-right: 5px;
}

.oh-kanban-card.is-loading .oh-kanban-card__profile-container {
  position: relative;
}
.oh-kanban-card.is-loading .oh-kanban-card__profile-container::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: 100%;
  height: 36%;
  z-index: 10;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
}
.oh-kanban-card.is-loading .oh-kanban-card__title,
.oh-kanban-card.is-loading .oh-kanban-card__subtitle {
  position: relative;
}
.oh-kanban-card.is-loading .oh-kanban-card__title::before,
.oh-kanban-card.is-loading .oh-kanban-card__subtitle::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: -webkit-fill-available;
  height: 90%;
  z-index: 10;
  border-radius: 4px;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
}

.oh-modal.is-loading .oh-profile__avatar {
  position: relative;
}
.oh-modal.is-loading .oh-profile__avatar::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: 64px;
  z-index: 10;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
}
.oh-modal.is-loading .oh-timeoff-modal__user {
  position: relative;
}
.oh-modal.is-loading .oh-timeoff-modal__user::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: -webkit-fill-available;
  height: 90%;
  z-index: 10;
  border-radius: 4px;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
}
.oh-modal.is-loading .oh-timeoff-modal__stat-title {
  position: relative;
}
.oh-modal.is-loading .oh-timeoff-modal__stat-title::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: -webkit-fill-available;
  height: 90%;
  z-index: 10;
  border-radius: 4px;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
}
.oh-modal.is-loading .oh-timeoff-modal__stat-count {
  position: relative;
}
.oh-modal.is-loading .oh-timeoff-modal__stat-count::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: -webkit-fill-available;
  height: 90%;
  z-index: 10;
  border-radius: 4px;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
}
.oh-modal.is-loading .oh-modal__dialog-footer .oh-btn {
  position: relative;
}
.oh-modal.is-loading .oh-modal__dialog-footer .oh-btn::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: -webkit-fill-available;
  height: 100%;
  z-index: 10;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
}

.oh-card.is-loading .oh-profile__avatar {
  position: relative;
}
.oh-card.is-loading .oh-profile__avatar::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: 64px;
  z-index: 10;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
}
.oh-card.is-loading .oh-timeoff-modal__user {
  position: relative;
}
.oh-card.is-loading .oh-timeoff-modal__user::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: -webkit-fill-available;
  height: 90%;
  z-index: 10;
  border-radius: 4px;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
}
.oh-card.is-loading .oh-timeoff-modal__stat-title {
  position: relative;
}
.oh-card.is-loading .oh-timeoff-modal__stat-title::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: -webkit-fill-available;
  height: 90%;
  z-index: 10;
  border-radius: 4px;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
}
.oh-card.is-loading .oh-timeoff-modal__stat-count {
  position: relative;
}
.oh-card.is-loading .oh-timeoff-modal__stat-count::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: -webkit-fill-available;
  height: 90%;
  z-index: 10;
  border-radius: 4px;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
}
.oh-card.is-loading .oh-modal__dialog-footer .oh-btn {
  position: relative;
}
.oh-card.is-loading .oh-modal__dialog-footer .oh-btn::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: -webkit-fill-available;
  height: 100%;
  z-index: 10;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
}

.oh-modal__dialog-footer .is-loading .oh-btn {
  position: relative;
}
.oh-modal__dialog-footer .is-loading .oh-btn::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: -webkit-fill-available;
  height: 100%;
  z-index: 10;
  background: linear-gradient(110deg, #e0e0e0 8%, #f0f0f0 18%, #e0e0e0 33%);
  background-size: 200%;
  animation: shimmer 1.2s infinite;
  z-index: 999;
}

.oh-timesheet-table {
  border: 1px solid hsl(213, 22%, 93%);
}
.oh-timesheet-table thead tr th {
  font-weight: bold;
}
.oh-timesheet-table th,
.oh-timesheet-table td {
  padding: 10px;
  text-align: left;
}
.oh-timesheet-table tr:hover {
  cursor: pointer;
}
.oh-timesheet-table .hidden-row {
  display: none;
}
.oh-timesheet-table .caret {
  transition: transform 0.3s ease;
  cursor: pointer;
}
.oh-timesheet-table .rotate {
  transform: rotate(90deg);
}
.oh-timesheet-table .caret {
  transition: transform 0.3s ease;
}

.oh-timesheet .oh-table tr th,
.oh-timesheet .oh-table tr td {
  padding: 0.6rem;
}
.oh-timesheet .oh-table tr th {
  color: #1c1c1c;
}
.oh-timesheet .accordion-header {
  position: relative;
  align-items: center;
  display: flex;
  justify-content: space-between;
  border: 1px solid hsl(213, 22%, 93%);
  background-color: hsl(0, 0%, 100%);
  padding: 0.5rem 0.5rem 0.5rem 2.5rem;
  cursor: pointer;
}
.oh-timesheet .accordion-header span {
  padding: 0.4rem 0;
  font-weight: bold;
}
.oh-timesheet .accordion .accordion-body {
  padding-left: 1.5rem;
}
.oh-timesheet .accordion .accordion-header::before {
  content: "▶";
  background-position: center;
  background-repeat: no-repeat;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: hsl(8, 61%, 50%);
  -o-object-fit: contain;
     object-fit: contain;
  position: absolute;
  left: 8px;
}
.oh-timesheet .accordion .accordion-header.active::before {
  content: "▼";
}
.oh-timesheet .table {
  width: 100%;
  border-collapse: collapse;
}
.oh-timesheet .table th,
.oh-timesheet .table td {
  border: 1px solid #ddd;
  padding: 0.6rem;
  text-align: left;
}
.oh-timesheet .accordion-header:hover {
  background-color: rgba(233, 237, 241, 0.4);
}
.oh-timesheet .accordion-body {
  display: none;
}
.oh-timesheet .accordion-body {
  border: 1px solid hsl(213, 22%, 93%);
  border-top: none;
  padding: 0.5rem;
  background-color: hsl(0, 0%, 100%);
}

.oh-hover-btn-table-drawer {
  position: absolute;
  top: 30px !important;
}

#pivot-container {
  border: 1px solid #ddd;
}
#pivot-container .pvtUiCell {
  background-color: #fff;
}
#pivot-container .pvtUiCell .pvtRenderer,
#pivot-container .pvtUiCell .pvtAggregator {
  border: transparent;
}

.pvtRenderer {
  background-color: transparent !important;
}
.pvtRenderer :focus {
  border: none !important;
  outline: none !important;
}
.pvtRenderer :focus {
  border: none !important;
  outline: none !important;
}

#pivot-container .pvtColLabel,
#pivot-container .pvtRowLabel {
  font-weight: 500 !important;
}

#pivot-container .pvtTable tbody tr th,
#pivot-container .pvtTable thead tr th {
  background-color: #fff !important;
  border: 1px solid #e9edf1 !important;
}

.pvtAxisContainer li {
  display: flex;
}

.pvtAxisContainer li span.pvtAttr {
  border-radius: 0 !important;
  flex: 1 1 auto;
  background-color: #f9f9f9 !important;
  position: relative;
}
.pvtAxisContainer li span.pvtAttr .pvtTriangle {
  position: absolute;
  right: 8px;
}

.pvtHorizList .ui-sortable-handle .pvtAttr {
  padding: 2px 26px 2px 8px !important;
}
.pvtAxisContainer,
.pvtVals {
  border: 1px solid #e9edf1 !important;
}

.pvtRendererArea {
  padding: 0 !important;
}

#pivot-container {
  width: 100%;
  overflow-x: auto;
}
#pivot-container .pvtTable {
  min-width: 1200px;
  width: 100%;
  min-width: unset;
  max-width: 100%;
  border-collapse: collapse;
  font-size: 16px;
  background-color: #ffffff;
  table-layout: auto;
}

.pvtUi {
  width: 100% !important;
}

#pivot-container .pvtUiCell .pvtAggregator,
#pivot-container .pvtUiCell .pvtRenderer {
  background-color: transparent !important;
}

.pvtAttrDropdown {
  border: 1px solid #eaeaea !important;
}

.pvtSearch {
  border: 1px solid hsl(213, 22%, 84%) !important;
  border-radius: 0rem !important;
  padding: 0.8rem 1.25rem !important;
  color: hsl(0, 0%, 11%) !important;
  margin-bottom: 4px !important;
}

.pvtFilterBox {
  padding: 16px;
  width: auto !important;
  border: 1px solid #e9edf1 !important;
}
.pvtFilterBox h4, .pvtFilterBox .h4 {
  font-weight: bold;
  font-size: 0.9rem;
  text-align: left;
  margin-left: 0 !important;
  margin-top: 0 !important;
}
.pvtFilterBox p {
  margin-bottom: 0 !important;
  display: flex;
  gap: 8px;
}
.pvtFilterBox p :nth-child(1) {
  width: 100%;
  border-radius: 0 !important;
  background-color: hsl(8, 77%, 56%);
  color: hsl(0, 0%, 100%);
  text-decoration: none;
  font-size: 0.9rem;
  padding: 0.65rem 1rem;
  border: none;
}
.pvtFilterBox p :nth-child(2) {
  width: 100%;
  border-radius: 0 !important;
  background-color: #f0f0f0;
  color: hsl(0, 0%, 16%);
  text-decoration: none;
  font-size: 0.9rem;
  padding: 0.65rem 1rem;
  border: none;
}
.pvtFilterBox p input {
  background-color: transparent !important;
}
.pvtFilterBox p button {
  border: none !important;
  margin-bottom: 4px;
}
.pvtFilterBox .pvtCheckContainer p :nth-child(1) {
  background-color: unset !important;
  width: auto;
  padding: 0.5rem 0 !important;
}
.pvtFilterBox .pvtCheckContainer p :nth-child(2) {
  background-color: transparent !important;
  width: auto;
}

#pivot-container .pvtTable td {
  border: 1px solid #e9edf1 !important;
}

#pivot-container .pvtTable tr:nth-child(2n) td {
  background-color: #fff !important;
  border: 1px solid #e9edf1 !important;
}

#pivot-container .pvtTable th {
  background-color: #007bff;
  padding: 12px;
  border: 1px solid #ccc;
  font-size: 18px;
}

#pivot-container .pvtTable td {
  padding: 12px;
  border: 1px solid #ccc;
  font-size: 16px;
  background-color: #fdfdfd;
  color: #333;
}

#pivot-container .pvtTable tr:nth-child(even) td {
  background-color: #f2f2f2;
}

#pivot-container .pvtRowLabel,
#pivot-container .pvtColLabel {
  background-color: #e9ecef;
  font-weight: bold;
  font-size: 16px;
}

#pivot-container .pvtTotalLabel {
  background-color: #6c757d;
  font-weight: bold;
  font-size: 16px;
}

#pivot-container .pvtGrandTotal {
  background-color: #343a40;
  font-weight: bold;
  font-size: 16px;
  color: #fff;
}

#pivot-container .pvtTable tbody tr th,
#pivot-container .pvtTable thead tr th {
  background-color: #e6eeee;
  border: 2px solid #cdcdcd;
  font-size: 10pt;
  padding: 8px;
}

.js-plotly-plot .plotlyjsicon {
  display: none !important;
}

.oh-tabs {
  border: 1px solid hsl(213, 22%, 84%);
}

.oh-tabs__tablist {
  list-style-type: none;
  padding-left: 0px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 0px;
}

.oh-tabs__tab,
.oh-tabs__new-tab-config {
  padding: 1rem 1.25rem;
  height: 50px;
  max-height: 50px;
  border-bottom: 1px solid hsl(213, 22%, 84%);
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}
.oh-tabs__tab:hover,
.oh-tabs__new-tab-config:hover {
  background-color: hsl(213, 22%, 93%);
  border-bottom: 1px solid hsl(213, 22%, 84%);
}

.oh-tabs__tab--active {
  background-color: hsl(0, 0%, 100%);
  border-left: 1px solid hsl(213, 22%, 84%);
  border-right: 1px solid hsl(213, 22%, 84%);
  border-bottom: 1px solid hsl(0, 0%, 100%);
}
.oh-tabs__tab--active:hover {
  color: rgba(28.05, 28.05, 28.05, 0.7);
  background-color: hsl(0, 0%, 100%);
  border-bottom: 1px solid hsl(0, 0%, 100%);
}

.oh-tabs__tab.oh-tabs__new-tab,
.oh-tabs__new-tab-config {
  max-width: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-left: 1px solid hsl(213, 22%, 84%);
}

.oh-tabs__tab--active {
  background-color: hsl(0, 0%, 100%);
  border-left: 1px solid hsl(213, 22%, 84%);
  border-right: 1px solid hsl(213, 22%, 84%);
  border-bottom: 1px solid hsl(0, 0%, 100%);
}
.oh-tabs__tab--active:hover {
  color: rgba(28.05, 28.05, 28.05, 0.7);
  background-color: hsl(0, 0%, 100%);
  border-bottom: 1px solid hsl(0, 0%, 100%);
}

.oh-tabs__tab--active .oh-tabs__close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
}

.oh-tabs__tablist .oh-tabs__tab--active:first-child {
  border-left: none;
}
.oh-tabs__tablist .oh-tabs__tab--active:nth-last-child(2) {
  border-right: none;
}

.oh-tabs__content {
  width: 100%;
  background-color: hsl(0, 0%, 100%);
  padding: 1.25rem;
}

.oh-tabs__content {
  display: none;
}

.oh-tabs__content--active {
  display: block;
}

.oh-tabs__tab.oh-tabs__new-tab {
  max-width: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.oh-tabs__close-btn {
  padding: 0 !important;
  display: none;
}

/* ==================================
*          GENERAL TABS
* ================================= */
.oh-general__tabs {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
  align-content: center;
}

.oh-general__tab {
  margin-right: 1rem;
  flex-grow: 1;
}
.oh-general__tab:last-child {
  margin-right: 0;
}
@media (max-width: 575.98px) {
  .oh-general__tab {
    margin-bottom: 1rem;
  }
}

.oh-general__tab-link {
  color: hsl(0, 0%, 11%);
  border-bottom: 3px solid transparent;
  padding-bottom: 0.75rem;
}
.oh-general__tab-link:hover {
  opacity: 0.7;
}
.oh-general__tab-link:hover, .oh-general__tab-link:focus, .oh-general__tab-link:focus-visible {
  text-decoration: none;
}
@media (max-width: 575.98px) {
  .oh-general__tab-link {
    padding: 0.5rem 0.75rem;
    border-bottom: none;
  }
}

.oh-general__tab-link--active {
  border-bottom: 3px solid hsl(8, 77%, 56%);
}
@media (max-width: 575.98px) {
  .oh-general__tab-link--active {
    border-bottom: none;
    background-color: rgba(229.194, 79.4444, 56.406, 0.1);
    color: hsl(8, 77%, 56%);
    border-radius: 0.25rem;
  }
  .oh-general__tab-link--active:focus, .oh-general__tab-link--active:focus-visible {
    color: hsl(8, 77%, 56%);
  }
}

.oh-profile-section__tab {
  width: 100%;
  justify-content: space-between;
}

.oh-general__tabs--no-grow {
  justify-content: flex-start;
}

.oh-general__tabs--no-grow > * {
  flex-grow: 0;
}

.oh-general__tabs--profile li {
  margin-right: 2rem;
}
.oh-general__tabs--profile li:last-child {
  margin-right: 0;
}
@media (max-width: 575.98px) {
  .oh-general__tabs--profile li {
    margin-right: 0.5rem;
  }
  .oh-general__tabs--profile li:last-child {
    margin-right: 0;
  }
}

/* =====================================
*         Tabs Mobable
* =================================== */
.oh-tabs__movable {
  border: 1px solid hsl(213, 22%, 84%);
  background-color: hsl(0, 0%, 100%);
  margin-top: 1rem;
  margin-bottom: 1rem;
  box-shadow: rgba(0, 0, 0, 0.04) 0px 3px 5px;
}
.oh-tabs__movable:first-child {
  margin-top: 0;
}
.oh-tabs__movable:last-child {
  margin-bottom: 0;
}
.oh-tabs__movable:hover .oh-tabs__movable-header {
  background-color: hsl(0, 0%, 97.5%);
}

.oh-tabs__movable-header {
  padding: 0.3rem 0.75rem;
  border-bottom: 1px solid hsl(213, 22%, 84%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: move;
}

.oh-tabs__movable-header--w-80 {
  width: 80%;
  position: relative;
}
.oh-tabs__movable-header--w-80:after {
  content: "";
  bottom: -1px;
  position: absolute;
  border-bottom: 1px solid hsl(213, 22%, 84%);
  left: 100%;
  width: 25%;
}

.oh-tabs__movable-title {
  font-weight: bold;
  font-size: 1.25rem;
}
@media (max-width: 575.98px) {
  .oh-tabs__movable-title {
    font-size: 1rem;
    max-width: 200px;
  }
}

.oh-tabs__movable-body {
  padding: 1.25rem;
}

.oh-tabs__action-bar {
  border-bottom: 1px solid hsl(213, 22%, 93%);
  padding-bottom: 0.8rem;
  margin-bottom: 1.25rem;
}

.oh-tabs__movable-close {
  background-color: transparent;
}

.oh-tabs__movable:only-child .oh-tabs__movable-close {
  display: none;
}

.oh-tabs__movable:nth-child(1):nth-last-child(2) .oh-tabs__movable-close {
  display: none;
}

.oh-tabs__table-group-header {
  margin-bottom: 1rem;
}

.oh-tabs__table-group {
  margin-top: 1.5rem;
}
.oh-tabs__table-group:first-child {
  margin-top: 0;
}

.oh-tabs__table-group-title {
  font-size: 1.25rem;
  font-weight: bold;
}

.oh-sticky-table--no-overflow {
  overflow-x: auto;
  overflow-y: unset !important;
}

.oh-sticky-table__dropdown {
  z-index: 9;
}

/* =====================================
*         Tabs view / preview
* =================================== */
.oh-tabs__view-buttons {
  display: flex;
  width: 150px;
}
.oh-tabs__view-buttons .tab-btn {
  flex: 1;
  padding: 8px;
  background-color: transparent;
  cursor: pointer;
  border: 1px solid #ccc;
  border-bottom: none;
  outline: none;
  text-align: center;
}
.oh-tabs__view-buttons .tab-btn:first-child {
  border-right: none !important;
}
.oh-tabs__view-buttons .tab-btn span {
  opacity: 0.5;
}
.oh-tabs__view-buttons .tab-btn.active {
  background: rgba(229.194, 79.4444, 56.406, 0.05);
  border: 1px solid #ccc;
  border-bottom: none;
  opacity: 1;
}
.oh-tabs__view-buttons .tab-btn.active span {
  opacity: 1;
}

.oh-tabs__view {
  display: none;
}
.oh-tabs__view.active {
  display: block;
}

.oh-timeoff-card {
  display: flex;
  align-items: center;
}
@media (max-width: 991.98px) {
  .oh-timeoff-card {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }
}

.oh-timeoff-card__icon-container {
  width: 54px;
  height: 54px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: hsl(8, 77%, 56%);
  margin-right: 1.5rem;
}

.oh-timeoff-card__icon {
  color: hsl(0, 0%, 100%);
  font-size: 2rem;
}

.oh-timeoff-card__title {
  display: block;
  font-weight: bold;
  font-size: 1.15rem;
  width: 100%;
}

.oh-timeoff-card__stat-container,
.oh-timeoff-modal__stats-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  margin-top: 0.35rem;
}

.oh-timeoff-card__stat,
.oh-timeoff-modal__stat {
  margin-right: 1.75rem;
}
.oh-timeoff-card__stat:last-child,
.oh-timeoff-modal__stat:last-child {
  margin-right: 0;
}

.oh-timeoff-card__stat-title,
.oh-timeoff-modal__stat-title {
  display: block;
  font-size: 0.8rem;
  color: hsl(0, 0%, 45%);
  margin-bottom: 0.25rem;
}

.oh-timeoff-card__stat-count {
  font-size: 1.05rem;
  font-weight: bold;
}

.oh-timeoff-modal__download-link {
  width: 100%;
  background: hsl(213, 22%, 93%);
  padding: 1rem;
  margin-top: 1.25rem;
  display: flex;
  align-items: center;
  color: hsl(0, 0%, 27%);
}
.oh-timeoff-modal__download-link:hover, .oh-timeoff-modal__download-link:focus, .oh-timeoff-modal__download-link:focus-visible {
  text-decoration: none;
  background-color: hsl(213, 22%, 84%);
}

.oh-timeoff-modal__profile-content .oh-profile__image {
  width: 64px;
  height: 64px;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}

.oh-timeoff__date-container {
  display: flex;
  align-items: center;
}
.oh-timeoff__date-container input:first-child {
  border-right: 0;
}

.oh-timeoff-modal__stats-container {
  width: 100%;
}

.oh-timeoff-modal__profile-content {
  margin-bottom: 1rem;
  width: 100%;
}

.oh-timeoff-modal__profile-info {
  display: flex;
  flex-direction: column;
}

.oh-timeoff-modal__stat-count {
  font-size: 0.9rem;
}

.oh-timeoff-modal__stat-description {
  overflow-y: auto;
  max-height: 100px;
}

.oh-timeoff-modal__footer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding-top: 1rem;
  padding-bottom: 1.25rem;
}

.oh-timeoff-modal__footer > * {
  margin-right: 5px;
}
.oh-timeoff-modal__footer > *:last-child {
  margin-right: 0;
}

.oh-timeoff-modal__user {
  font-size: 1.2rem;
  margin-top: 0.75rem;
}

.oh-timeoff-modal__body {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.oh-timeoff-modal__position {
  color: rgba(114.75, 114.75, 114.75, 0.8);
}

.oh-modal__dialog-relative {
  position: relative;
}

.oh-modal__diaglog-nav {
  width: 2rem;
  height: 2rem;
  background: hsl(0, 0%, 100%);
  border-radius: 50px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.oh-modal__nav-prev {
  position: absolute;
  left: -55px;
  top: 50%;
  transform: translate(50%, -50%);
}

.oh-modal__nav-next {
  position: absolute;
  right: -25px;
  top: 50%;
  transform: translate(50%, -50%);
}

div.ui-tooltip {
  background-color: hsl(0, 0%, 11%);
  color: hsl(0, 0%, 100%);
  box-shadow: none;
  border: none;
  font-size: 0.8rem;
  border: none !important;
}

.oh-filter-tag-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.oh-filter-tag {
  position: relative;
  background-color: hsl(0, 0%, 100%);
  color: hsl(0, 0%, 11%);
  border-radius: 25px;
  border: 1px solid hsl(213, 22%, 93%);
  padding: 0.25rem 0.5rem;
  font-size: 0.7rem;
  font-weight: bold;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.4s ease-in-out;
}
.oh-filter-tag:hover {
  border-color: hsl(213, 22%, 84%);
  transition: all 0.4s ease-in-out;
}
.oh-filter-tag:hover .oh-filter-tag__close {
  color: hsl(8, 77%, 56%);
}
.oh-filter-tag:hover .oh-filter-tag__close::before {
  background-color: hsl(213, 22%, 84%);
  transition: all 0.4s ease-in-out;
}

.oh-filter-tag__close {
  background-color: transparent;
  color: hsl(0, 0%, 37%);
  padding: 0;
  margin-left: 0.75rem;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease-in-out;
}
.oh-filter-tag__close::before {
  content: "";
  width: 0.5px;
  position: relative;
  left: -3px;
  height: 27px;
  background-color: hsl(213, 22%, 93%);
  transition: all 0.4s ease-in-out;
}
.oh-filter-tag__close:hover {
  color: hsl(8, 61%, 50%);
  transition: all 0.4s ease-in-out;
}

.oh-titlebar-container__filters {
  margin-top: 1.5rem;
  gap: 0.75rem;
}

.oh-titlebar__tag {
  display: inline-block;
  background-color: hsl(0, 0%, 100%);
  color: hsl(0, 0%, 11%);
  border-radius: 25px;
  font-size: 0.85rem;
  padding: 0.25rem 0.75rem;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid rgba(229.194, 79.4444, 56.406, 0.15);
  transition: background-color 0.3s ease-in-out;
}
.oh-titlebar__tag:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transition: background-color 0.3s ease-in-out;
}

.oh-titlebar__tag-close {
  background-color: hsl(8, 77%, 56%);
  color: hsl(0, 0%, 100%);
  width: 16px;
  height: 16px;
  padding: 0;
  display: inline-flex;
  justify-content: center;
  font-weight: bold;
  align-items: center;
  border: none;
  border-radius: 50%;
  margin-left: 0.75rem;
}
.oh-titlebar__tag-close:hover {
  background-color: hsl(8, 61%, 50%);
}

.oh-titlebar__save {
  background-color: hsl(148, 71%, 44%);
  border: 1px solid hsl(148, 70%, 40%);
  border-radius: 25px;
  font-size: 0.85rem;
  padding: 0.25rem 0.75rem;
  color: hsl(0, 0%, 100%);
  display: flex;
  align-items: center;
  gap: 2px;
}
.oh-titlebar__save:hover {
  background-color: hsl(148, 70%, 40%);
}

.oh-modal__dialog--custom {
  max-width: 800px;
}
.oh-modal__dialog--custom .oh-modal__dialog-header {
  display: flex;
  justify-content: space-between;
}
.oh-modal__dialog--custom .oh-modal__close--custom {
  background-color: transparent;
  border: none;
}
.oh-modal__dialog--custom .oh-general__tab .oh-general__tab-link {
  padding: 0;
  transition: 0s;
}
.oh-modal__dialog--custom .oh-general__tab .oh-general__tab-link--active {
  background-color: #e54f38;
  border: 1px solid #e54f38;
  color: #fff;
  padding: 5px 12px;
  border-radius: 23px;
  transition: none;
}
.oh-modal__dialog--custom .oh-inner-sidebar {
  height: 100%;
}

.oh-modal__dialog-process {
  max-width: 900px;
}
.oh-modal__dialog-process .oh-modal__dialog-body-process .oh-inner-sidebar {
  padding: 0px 14px 14px 0;
}
.oh-modal__dialog-process .oh-modal__dialog-body-process .oh-inner-sidebar .oh-profile {
  align-items: start;
}
.oh-modal__dialog-process .oh-modal__dialog-body-process .oh-inner-sidebar .oh-card--no-shadow {
  border: 1px solid transparent !important;
  border-bottom: 1px solid #e9edf1 !important;
  margin-bottom: 10px;
}
.oh-modal__dialog-process .oh-modal__dialog-body-process .oh-inner-sidebar .oh-timeoff-modal__profile-info {
  align-items: start;
}
.oh-modal__dialog-process .oh-modal__dialog-body-process .oh-inner-sidebar .oh-timeoff-modal__profile-info .oh-timeoff-modal__user {
  font-size: 16px;
  margin-top: 0;
}
.oh-modal__dialog-process .oh-modal__dialog-body-process .oh-inner-sidebar-content .oh-profile__avatar img {
  width: 90px;
  height: 90px;
}
.oh-modal__dialog-process .oh-modal__dialog-body-process .oh-inner-sidebar-content .oh-profile_info-card {
  border-bottom: 1px solid hsl(213, 22%, 93%);
  padding-bottom: 16px;
}
.oh-modal__dialog-process .oh-modal__dialog-body-process .oh-inner-sidebar-content .oh-profile_info-card .oh-profile__info-designation {
  text-align: center;
}
.oh-modal__dialog-process .oh-modal__dialog-body-process .oh-layout--grid-3 {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(275px, 1fr)) !important;
  gap: 1%;
  justify-content: space-between;
}
.oh-modal__dialog-process .oh-modal__dialog-body-process .oh-profile__image {
  width: 36px;
  height: 36px;
}
.oh-modal__dialog-process .oh-modal__dialog-body-process .oh-general__custom-tabs {
  border-bottom: none;
}

.oh-kanban-card-action {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  transition: border-color 0.2s ease-in-out;
  cursor: pointer;
  position: relative;
}
.oh-kanban-card-action__checkbox {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}
.oh-kanban-card-action__checkbox-label {
  width: 18px;
  height: 18px;
  border: 2px solid #ccc;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: absolute;
  right: 10px;
  top: 10px;
  transition: all 0.2s ease-in-out;
}
.oh-kanban-card-action__checkbox-label::before {
  content: "";
  width: 10px;
  height: 10px;
  background: transparent;
  border-radius: 12px;
  transition: background 0.2s ease-in-out;
}
.oh-kanban-card-action:has(.oh-kanban-card-action__checkbox:checked) {
  border-color: #ffa89b;
  background: #fff2f0;
}
.oh-kanban-card-action .oh-kanban-card-action__checkbox:checked + .oh-kanban-card-action__checkbox-label::before {
  background: #e54f38;
  border-radius: 12px;
}

.oh-layout_horizontal {
  display: flex;
  flex-direction: row;
  overflow-x: auto;
  gap: 12px;
  margin: 16px 0;
  padding-bottom: 8px;
}
.oh-layout_horizontal .oh-kanban-card {
  background-color: #fff;
  border: 1px solid #cdd5df;
  border-radius: 16px;
  min-width: 200px;
  padding: 0.7rem;
  align-items: start;
}
.oh-layout_horizontal .oh-kanban-card__profile-container {
  width: 42px;
  height: 42px;
}

.oh-modal__dialog-process .oh-modal__dialog-body-process .oh-inner-sidebar-content .oh-profile__avatar .oh-table-img {
  width: 30px !important;
  height: 30px !important;
}

.oh-hands svg {
  width: 36px;
  height: auto;
}

.oh-main__titlebar-title {
  font-size: 1.5rem;
}

.oh-titlebar-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.oh-titlebar-container__buttons {
  display: flex;
  align-items: center;
}

.oh-titlebar-container__filters {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.oh-main__topbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 2rem;
  padding-bottom: 2rem;
}
@media (max-width: 767.98px) {
  .oh-main__topbar {
    flex-direction: column;
    align-items: flex-start;
  }
}

.oh-main__titlebar--right {
  display: flex;
  align-items: center;
}
@media (max-width: 767.98px) {
  .oh-main__titlebar--right {
    width: 100%;
  }
}

@media (max-width: 767.98px) {
  .oh-main__titlebar--right > * {
    flex-basis: 0;
    flex-grow: 1;
  }
}

.oh-main__titlebar-button-container {
  display: flex;
  align-items: center;
}
@media (max-width: 767.98px) {
  .oh-main__titlebar-button-container {
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
  }
  .oh-main__titlebar-button-container button {
    margin-left: 0;
  }
}

.oh-main__titlebar-button-container > *:first-child {
  margin-left: 15px;
}
@media (max-width: 767.98px) {
  .oh-main__titlebar-button-container > *:first-child {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.oh-main__titlebar-search-toggle {
  display: block;
  padding: 0.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 0.3rem;
  border: 1px solid hsl(213, 22%, 84%);
}

.oh-main__titlebar-serach-icon {
  font-size: 1.2rem;
}

@media (max-width: 1000px) {
  .oh-main__titlebar--left {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}

.oh-main__titlebar-search-toggle {
  display: none !important;
}
@media (max-width: 1000px) {
  .oh-main__titlebar-search-toggle {
    display: flex !important;
  }
}

@media (max-width: 991.98px) {
  .oh-main__sidebar-visible {
    width: 1200px;
  }
}

@media (max-width: 767.98px) {
  .oh-main__section--reverse-sm > div:first-child {
    order: 2;
  }
  .oh-main__section--reverse-sm > div:last-child {
    order: 1;
  }
}
/* =================================
*          QUESTIONNAIRE
* ================================ */
.oh-section-edit--delete {
  position: absolute;
  left: 0.09rem;
}

/* =================================
*          VIEW TYPES
* ================================ */
.oh-view-types {
  list-style-type: none;
  padding-left: 0px;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 0px;
}
@media (max-width: 767.98px) {
  .oh-view-types {
    height: 100%;
  }
}

.oh-view-type {
  background-color: hsl(0, 0%, 11%);
  border: 1px solid hsl(213, 22%, 84%);
  height: 48.84px;
}
@media (max-width: 767.98px) {
  .oh-view-type {
    height: 100%;
    height: 46.8px;
  }
}
@media (max-width: 575.98px) {
  .oh-view-type {
    flex-basis: 0;
    flex-grow: 1;
    height: 43.5px;
  }
}
.oh-view-type:first-child {
  border-right: 0;
}

.oh-btn--view {
  height: 100%;
}

/* =================================
*          PERMISSIONS
* ================================ */
.oh-permission-panel,
.oh-user-panel {
  background-color: hsl(0, 0%, 97.5%);
  border: 2px solid hsl(213, 22%, 93%);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0 0 1rem;
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.oh-permission-panel:hover,
.oh-user-panel:hover {
  background-color: hsl(0, 0%, 96%);
}

.oh-user-panel {
  padding: 0.5rem 0 0.5rem 0.75rem;
}

.oh-permission-panel__remove,
.oh-user-panel__remove {
  background-color: transparent;
  border: none;
  border-left: 2px solid hsl(213, 22%, 93%);
  width: 35px;
  height: 35px;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.5rem;
}
.oh-permission-panel__remove:hover,
.oh-user-panel__remove:hover {
  color: hsl(1, 100%, 61%);
}

.oh-user-panel__remove {
  border-left: none;
}

.oh-permission-table__collapse {
  border: none;
  display: flex;
  align-items: center;
  padding: 0.5rem;
  font-size: 1.25rem;
  visibility: hidden;
}

.oh-sticky-table__tr:hover .oh-permission-table__collapse {
  visibility: visible;
}

.oh-permission-table__tr .oh-sticky-table__sd {
  width: 35%;
}

.oh-permission-count {
  width: 100%;
  display: flex;
  padding: 0.5rem 0rem;
  font-style: italic;
  color: hsl(0, 0%, 37%);
  width: 100%;
}

.oh-permission-table--collapsed .oh-permission-panel,
.oh-permission-table--collapsed .oh-user-panel {
  display: none;
}

.oh-permission-table--collapsed .oh-permission-table__collapse .oh-permission-table__collapse-up {
  display: none;
}

.oh-permission-table--collapsed .oh-permission-table__collapse .oh-permission-table__collapse-down {
  display: block;
}

.oh-permission-table__collapse .oh-permission-table__collapse-up {
  display: block;
}

.oh-permission-table__collapse .oh-permission-table__collapse-down {
  display: none;
}

@media (max-width: 575.98px) {
  .oh-permission-table__collapse {
    display: none !important;
  }
}
/* =================================
*          QUESTIONNAIRE
* ================================ */
.oh-empty {
  width: 100%;
  height: calc(100vh - 400px);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.oh-empty__message {
  font-size: 1rem;
  color: hsl(0, 0%, 45%);
  text-align: center;
  max-width: 600px;
}

/* =================================
*         DASHBOARD PAGE
* ================================ */
.oh-dashboard {
  padding-top: 2.5rem;
  max-width: 100%;
}
@media (max-width: 767.98px) {
  .oh-dashboard {
    margin: 0 !important;
  }
}

@media (max-width: 991.98px) {
  .oh-dashboard__cards > div,
  .oh-dashboard__movable-cards > div {
    margin-bottom: 1rem;
  }
}

.oh-dashboard__cards--3 {
  grid-template-columns: 1fr 1fr 1fr;
}

.oh-card-dashboard--no-scale:hover {
  transform: scale(100%) !important;
}

.oh-dashboard__movable-cards--2 {
  grid-template-columns: 1fr 1fr;
}

.oh-card-dashboard__title {
  font-weight: bold;
}

.oh-card-dashboard__count {
  font-size: 3rem;
}

.oh-card-dashboard {
  width: 100%;
  background-color: hsl(0, 0%, 100%);
  padding: 1rem;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
  border-top: 5px solid;
  transition: all 0.4s ease-in-out;
  cursor: pointer;
}
.oh-card-dashboard:hover {
  transition: all 0.4s ease-in-out;
  transform: scale(102%);
}

.oh-card-dashboard__header {
  margin-bottom: 0.75rem;
}

.oh-card-dashboard__header--divider {
  border-bottom: 1px solid hsl(213, 22%, 93%);
  padding-bottom: 1rem;
  margin-bottom: 1.25rem;
}

.oh-card-dashboard__sign {
  font-size: 1.2rem;
}

.oh-card-dashboard__counts {
  display: flex;
  align-items: baseline;
}

.oh-card-dashboard__badge {
  border-radius: 25px;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  font-weight: bold;
}

.oh-card-dashboard--success {
  border-color: hsl(148, 70%, 40%);
}
.oh-card-dashboard--success .oh-card-dashboard__badge {
  background-color: hsl(148, 70%, 40%);
  color: hsl(0, 0%, 100%);
}
.oh-card-dashboard--success .oh-card-dashboard__sign {
  color: hsl(148, 70%, 40%);
}

.oh-card-dashboard--danger {
  border-color: hsl(1, 64%, 49%);
}
.oh-card-dashboard--danger .oh-card-dashboard__badge {
  background-color: hsl(1, 64%, 49%);
  color: hsl(0, 0%, 100%);
}
.oh-card-dashboard--danger .oh-card-dashboard__sign {
  color: hsl(1, 64%, 49%);
}

.oh-card-dashboard--warning {
  border-color: hsl(37, 90%, 47%);
}
.oh-card-dashboard--warning .oh-card-dashboard__badge {
  background-color: hsl(37, 90%, 47%);
  color: hsl(0, 0%, 100%);
}
.oh-card-dashboard--warning .oh-card-dashboard__sign {
  color: hsl(37, 90%, 47%);
}

.oh-card-dashboard--neutral {
  border-color: hsl(216, 18%, 64%);
}
.oh-card-dashboard--neutral .oh-card-dashboard__badge {
  background-color: hsl(216, 18%, 64%);
  color: hsl(0, 0%, 100%);
}
.oh-card-dashboard--neutral .oh-card-dashboard__sign {
  color: hsl(216, 18%, 64%);
}

.oh-card-dashboard--transparent {
  border-color: transparent;
}
.oh-card-dashboard--transparent .oh-card-dashboard__badge {
  background-color: transparent;
  color: hsl(0, 0%, 100%);
}
.oh-card-dashboard--transparent .oh-card-dashboard__sign {
  color: transparent;
}

.oh-dashbaord__events-reel {
  display: flex;
  transition: all 0.3s ease-in-out;
}

.oh-dashboard__event {
  width: 100%;
  color: hsl(0, 0%, 100%);
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
  padding: 1.25rem 1.25rem 2.5rem;
  display: flex;
  align-items: flex-start;
  flex-shrink: 0;
  flex-grow: 1;
}

.oh-dashboard__event--purple {
  background-color: rebeccapurple;
}

.oh-dashboard__event--crimson {
  background-color: crimson;
}

.oh-dasboard__event-photo {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.75rem;
  border: 4px solid rgba(255, 255, 255, 0.3);
}

.oh-dasboard__event-photo,
.oh-dasboard__event-details {
  flex-shrink: 0;
}

.oh-dashboard__event-userphoto {
  width: 100%;
  height: 100%;
}

.oh-dasboard__event-details {
  display: flex;
  flex-direction: column;
}

.oh-dashboard__event-title {
  font-weight: bold;
  font-size: 0.8rem;
}

.oh-dashboard__event-main {
  font-size: 1.35rem;
  margin-bottom: 0.15rem;
}

.oh-dashboard__event-date {
  font-size: 0.8rem;
}

.oh-dashboard__events {
  position: relative;
  max-width: 100%;
  overflow: hidden;
  display: flex;
}

.oh-dashboard__events-nav {
  display: flex;
  justify-content: center;
  align-items: center;
  list-style: none;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  padding-left: 0;
}

.oh-dashboard__events-nav-item {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  margin-right: 0.25rem;
  cursor: pointer;
}
.oh-dashboard__events-nav-item:hover {
  background-color: rgba(255, 255, 255, 0.5);
}
.oh-dashboard__events-nav-item:last-child {
  margin-right: 0;
}

.oh-dashboard__events-nav-item--active {
  background-color: rgba(255, 255, 255, 0.8);
}

.oh-card-dashboard__user-list {
  padding-left: 0;
  list-style: none;
  margin-bottom: 0;
}

.oh-card-dashboard__user-item {
  margin-bottom: 0.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px dashed hsl(213, 22%, 93%);
}
.oh-card-dashboard__user-item:last-child {
  margin-bottom: 0;
  border: none;
}

.oh-dashboard-card {
  background-color: hsl(0, 0%, 100%);
  border: 1px solid hsl(213, 22%, 84%);
  padding: 1.25rem;
}

/* ==============================
*           OrgChart
* ============================ */
.oh-main__org-chart-data {
  display: none;
}

.oh-profile-section {
  background-color: hsl(0, 0%, 100%);
  border: 1px solid hsl(213, 22%, 84%);
  padding: 1.25rem;
}

.oh-profile-section__sidebar {
  height: 100%;
  border-right: 1px solid hsl(213, 22%, 84%);
}

.oh-profile-section__sidebar-items {
  padding-left: 0px;
  list-style-type: none;
}

.oh-profile-section__sidebar-item {
  padding: 0.75rem 0;
}

.oh-profile-section__sidebar-item--active {
  border-right: 4px solid hsl(8, 77%, 56%);
}

.oh-profile-section__sidebar-link:hover, .oh-profile-section__sidebar-link:focus, .oh-profile-section__sidebar-link:focus-visible {
  text-decoration: none;
}

.oh-profile-section__edit-photo {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  transition: all 0.4s ease-in-out;
}
.oh-profile-section__edit-photo:hover::after {
  content: "Edit";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 30px;
  display: flex;
  justify-content: center;
  color: hsl(0, 0%, 100%);
  background: rgba(0, 0, 0, 0.6);
  padding: 0.25rem;
  transition: all 0.4s ease-in-out;
}

.oh-profile-section__avatar {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

@media (max-width: 767.98px) {
  .oh-profile-section__topbar {
    flex-direction: row !important;
  }
}
@media (max-width: 575.98px) {
  .oh-profile-section__topbar {
    padding-bottom: 0.75rem;
    flex-direction: column !important;
  }
  .oh-profile-section__topbar .oh-profile-section__tab {
    margin-top: 1.5rem;
  }
}

.oh-profile-section__modal-avatar {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  overflow: hidden;
}
@media (max-width: 767.98px) {
  .oh-profile-section__modal-avatar {
    max-width: 100px;
    height: 100px;
  }
}

.oh-profile-section__modal-image {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.oh-profile-section__image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.oh-history_date {
  position: relative;
  text-align: center;
  margin: 1.6rem 0;
}

.oh-history_date::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  left: 0;
  right: 0;
  background-color: hsl(213, 22%, 93%);
  top: 10px;
}

.oh-history_date-content {
  z-index: 1;
  padding: 6px 24px;
  background-color: hsl(0, 0%, 100%);
  position: relative;
}

.oh-history_user-img {
  width: 40px;
  height: 40px;
  margin-right: 0.6rem;
  position: relative;
}

.oh-history_user-pic {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.oh-history_time {
  color: hsl(216, 3%, 39%);
  font-size: 0.875em;
}

.oh-history__username {
  font-size: 1rem;
}

.oh-history_msg-container {
  background-color: hsl(0, 0%, 97.5%);
  border: 1px solid hsl(213, 22%, 93%);
  border-radius: 0 0.6rem 0.6rem 0.6rem;
  padding: 0.8rem 1rem;
  width: -moz-fit-content;
  width: fit-content;
  margin: 0.4rem 0;
  font-size: 0.9rem;
}

.oh-history_msging-email {
  background-color: hsl(0, 75%, 97%);
  color: hsl(1, 100%, 61%);
  padding: 0.2rem 0.6rem;
  border-radius: 0.2rem;
  margin-right: 0.2rem;
  font-size: 0.8rem;
}

.oh-history_task-state {
  color: hsl(0, 0%, 27%);
  font-size: 0.9rem;
}

.oh-progress_arrow {
  width: 0.8rem;
  height: auto;
}

.oh-history_task-tracker {
  padding: 0.2rem 0;
  font-size: 0.85rem;
}

.oh-history_task-list {
  padding: 0.2rem 0;
}

.oh-history_tracking-value {
  color: hsl(1, 100%, 61%);
}

.oh-history-task-state {
  color: hsl(216, 3%, 39%);
}

.oh-history_user-state {
  position: absolute;
  width: 0.6rem;
  height: 0.6rem;
  border-radius: 50%;
  right: 2px;
  bottom: 0px;
  cursor: pointer;
}

.oh-history_user-details {
  flex: 1;
}

.oh-user_inactive {
  background-color: rgb(153, 153, 153);
  border: 2px solid hsl(0, 0%, 100%);
}

.oh-history_abt {
  width: 50%;
  margin-top: 0.2rem;
  margin-bottom: 1.6rem;
}
@media (max-width: 767.98px) {
  .oh-history_abt {
    width: 100%;
  }
}

.oh-history_tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 0.4rem;
}

.oh-history_tabs-items {
  border-radius: 1rem;
  border: 1px solid;
  font-size: 0.7rem;
}

.oh-history_log-container {
  margin-top: 2rem;
  display: flex;
}

.oh-history-log_msg-section {
  width: 100%;
}

.oh-history-log_msg-container {
  border-radius: 0.6rem;
  border: 1px solid hsl(213, 22%, 84%);
  width: 100%;
  padding: 1rem;
}

.oh-history_textarea {
  border: none;
  outline: none;
  width: 100%;
  border-bottom: 1px solid hsl(213, 22%, 84%);
  font-size: 0.8rem;
  padding: 0.4rem 0;
  resize: none;
}

.oh-history_cust_btn {
  display: flex;
}

.oh-history-log_buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.oh-history_iconbtn {
  padding: 0 0.2rem;
}

.oh-history_tabs-items:hover {
  color: hsl(0, 0%, 100%);
  text-decoration: none;
  background-color: hsl(8, 77%, 56%);
}

.oh-wrapper-main--1 {
  grid-template-columns: auto;
}

.members-container {
  background-color: #f9f9f9;
  border: 1px solid #e7e7e7;
  padding: 8px;
  max-width: -moz-fit-content;
  max-width: fit-content;
  border-radius: 45px;
}
.members-container .oh-faq_search--icon {
  top: 12px;
}

.members-container ul li {
  list-style: none;
  float: left;
  border: 2px solid #f9f9f9;
}

.members-container ul .li-last {
  margin-right: 0px;
  background-color: #f2f3f5;
  width: 32px;
  height: 32px;
  border-radius: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
}

.members-container ul .li-last a {
  text-decoration: none;
  position: relative;
}

.img-right-arrow,
.img-left-arrow {
  float: right;
  margin-right: 6px;
}

.members-container ul .active {
  border: 2px solid #e54f38;
  border-radius: 50%;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: #fff;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  top: 168px;
  padding: 10px;
  border-radius: 10px;
}
.dropdown-content .oh-input {
  padding: 0.4rem 1rem;
  padding-left: 28px;
}

.dropdown-content a {
  color: black;
  padding: 6px 10px;
  text-decoration: none;
  display: block;
  border-radius: 10px;
}

.dropdown-content a span {
  padding-left: 5px;
}

.dropdown-content a img {
  width: 28px;
}

.dropdown-content a:hover {
  background-color: #e7e7e9;
  border-radius: 12px;
}

.show {
  display: block;
}

a.oh-profile-dropdown-link:hover {
  text-decoration: none;
}

.oh-sorting-field .oh-card-dashboard--moveable:hover {
  cursor: grab;
}

.oh-card-dashboard--moveable {
  position: relative;
}

.oh-state-highlight {
  background-color: #f0f0f0;
  border: 1px dashed #ccc;
  line-height: 1.2em;
  min-height: 2rem;
  height: auto;
  width: 100%;
  margin-top: 1rem;
  box-sizing: border-box;
}
@media (max-width: 992px) {
  .oh-state-highlight {
    width: 100%;
  }
}

.oh-onboarding {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  min-height: calc(100vh - 4rem);
  margin-top: 2rem;
  margin-bottom: 2rem;
}
@media (max-width: 767.98px) {
  .oh-onboarding {
    align-items: flex-start;
    height: 100%;
    margin-top: 2rem;
  }
}

.oh-onboarding-card {
  background-color: hsl(0, 0%, 100%);
  width: 90%;
  max-width: 600px;
  padding: 2.5rem;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
}
@media (min-width: 1400px) {
  .oh-onboarding-card {
    max-width: 800px;
  }
}

.oh-onboarding-card--top-bar, .oh-onboarding-preview--top-bar {
  position: relative;
}
.oh-onboarding-card--top-bar::before, .oh-onboarding-preview--top-bar::before {
  content: "";
  left: 0;
  top: 0;
  height: 5px;
  width: 100%;
  background-color: hsl(8, 77%, 56%);
  position: absolute;
}

.oh-onboarding-card__container {
  width: 98%;
  max-width: 350px;
  margin: 0 auto;
}

.oh-onboarding-card__container--wide {
  max-width: 650px;
}

.oh-onboarding-card__title {
  font-weight: bold;
}

.oh-onboarding-card__title--h1 {
  font-size: 1.75rem;
}

.oh-onboarding-card__title--h2 {
  font-size: 1.45rem;
}

.oh-onboarding-card__title--h3 {
  font-size: 1.2rem;
}

.oh-onboarding-card__lead {
  font-size: 1.05rem;
  color: hsl(0, 0%, 45%);
}

.oh-onboarding-card__button {
  margin: 0 auto;
}

.oh-onboarding-card__lead-image {
  width: 300px;
  display: block;
  height: auto;
  margin: 0 auto;
}

.oh-onboarding-card__steps {
  list-style-type: none;
  padding-left: 0;
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

.oh-onboarding-card__step {
  display: flex;
  align-items: center;
  margin-right: 1.25rem;
  flex-shrink: 0;
}
.oh-onboarding-card__step:last-child {
  margin-right: 0;
}
@media (max-width: 575.98px) {
  .oh-onboarding-card__step {
    display: none;
    margin-right: 0;
  }
}

.oh-onboarding-card__step--active .oh-onboarding-card__text {
  color: hsl(0, 0%, 11%);
  font-weight: bold;
}
@media (max-width: 575.98px) {
  .oh-onboarding-card__step--active {
    display: flex;
  }
}

.oh-onboarding-card__count {
  background-color: rgba(229.194, 79.4444, 56.406, 0.1);
  color: hsl(8, 77%, 56%);
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
  font-size: 0.75rem;
}

.oh-onboarding-card__text {
  color: hsl(0, 0%, 45%);
  font-size: 0.8rem;
}

.oh-onboarding-card__header {
  border-bottom: 1px solid hsl(213, 22%, 93%);
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.oh-onboarding-card__form {
  width: 98%;
  max-width: 400px;
  margin: 0 auto;
}

.oh-onboarding-card__greeting {
  color: hsl(0, 0%, 45%);
  font-size: 1.2rem;
}

.oh-onboarding-card__highlight-code {
  display: block;
  max-width: 220px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  padding: 1rem 1.25rem;
  background-color: hsl(213, 22%, 93%);
  border: 1px dashed hsl(213, 22%, 84%);
}

.oh-onboarding-card__highlight-label {
  font-weight: bold;
}

.oh-onboarding-card__subtitle {
  font-size: 0.9rem;
  color: hsl(0, 0%, 45%);
  display: block;
}

.oh-onboarding-card__company-name {
  font-size: 0.85rem;
  font-weight: bold;
}

.oh-onboarding-card__section-title {
  font-size: 1.15rem;
  font-weight: bold;
}

.oh-onboarding-card__address-group {
  margin-top: 2rem;
}
.oh-onboarding-card__address-group:first-child {
  margin-top: 0;
}

.oh-onboarding-card__collapse-header {
  margin-top: 1rem;
  margin-bottom: 1rem;
  padding: 1.5rem 0 1rem;
  border-bottom: 1px solid hsl(213, 22%, 84%);
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  position: relative;
}
.oh-onboarding-card__collapse-header:hover {
  background-color: rgba(233.223, 236.7573, 241.077, 0.3);
  transition: all 0.3s ease-in-out;
}
.oh-onboarding-card__collapse-header::after {
  content: "";
  position: absolute;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzFjMWMxYyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBzdHJva2Utd2lkdGg9IjEuNSIgdmlld0JveD0iMCAwIDI0IDI0IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogIDxwYXRoIGQ9Im01LjI1IDguNjI1IDYuNzUgNi43NSA2Ljc1LTYuNzUiPjwvcGF0aD4KPC9zdmc+");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  width: 20px;
  height: 20px;
  right: 0;
  top: 24px;
  transition: all 0.3s ease-in-out;
}

.oh-onboarding-card__collapse--show .oh-onboarding-card__collapse-header {
  margin-bottom: 0;
}
.oh-onboarding-card__collapse--show .oh-onboarding-card__collapse-header::after {
  content: "";
  transform: rotate(-180deg);
  transition: all 0.3s ease-in-out;
}

.oh-onboarding-card__collapse-title {
  font-weight: bold;
}

.oh-onboarding-card__collappse-body {
  padding: 1.5rem 0;
  display: none;
}

.oh-onboarding-card__collapse--show .oh-onboarding-card__collappse-body {
  display: block;
}

.oh-onboarding--preview {
  display: flex;
  align-items: flex-start;
}
@media (max-width: 991.98px) {
  .oh-onboarding--preview {
    flex-direction: column;
    align-items: center;
  }
}

.oh-onboarding__info-container {
  display: flex;
  align-items: flex-start;
}
@media (max-width: 575.98px) {
  .oh-onboarding__info-container {
    flex-direction: column;
  }
}

.oh-onboarding__title-container {
  margin-left: 10px;
}
@media (max-width: 575.98px) {
  .oh-onboarding__title-container {
    margin-top: 10px;
    margin-left: 0;
  }
}

.oh-onboarding__title {
  font-size: 2rem;
  line-height: 1.25;
  font-weight: bold;
}

.oh-onboarding-preview {
  width: calc((100vw - 200px) / 2 - 40px);
  max-width: 500px;
  margin-left: 15px;
  background-color: hsl(0, 0%, 100%);
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
  padding: 2rem 1rem;
}
.oh-onboarding-preview img {
  width: 100%;
  height: auto;
  -o-object-fit: contain;
     object-fit: contain;
}
@media (max-width: 991.98px) {
  .oh-onboarding-preview {
    width: 90%;
    max-width: 600px;
    margin-left: 0;
    margin-top: 15px;
  }
}

.oh-feedback-card__name-container, .oh-feedback-card__actions {
  display: flex;
  flex-direction: column;
}

.oh-feedback-card__details {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.oh-feedback-card__profile-container {
  display: flex;
}

.oh-feedback-card__feedback-status {
  font-size: 0.85rem;
  font-weight: bold;
}

.oh-feedback-card__name {
  font-size: 1.15rem;
  font-weight: bold;
}

.oh-feedback-care__header {
  padding-bottom: 0.75rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid hsl(213, 22%, 93%);
}

.oh-payslip {
  max-width: 1200px;
  width: 100%;
  margin: 2rem auto;
  padding: 1.5rem;
  border: 1px solid hsl(213, 22%, 84%);
}
@media (max-width: 991.98px) {
  .oh-payslip {
    margin: 0;
    border: none;
  }
}

.oh-payslip__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid hsl(213, 22%, 84%);
}

.oh-payslip__title {
  font-weight: bold;
}

.oh-payslip__title--h1 {
  font-size: 1.5rem;
}

.oh-payslip__title--h2 {
  font-size: 1rem;
}

.oh-payslip__employee-title {
  font-size: 0.9rem;
  font-weight: bold;
  color: hsl(0, 0%, 37%);
}

.oh-payslip__employee-details {
  padding-left: 0;
  list-style-type: none;
}

.oh-payslip__employee-detail {
  font-size: 0.9rem;
  padding-top: 0.95rem;
  display: flex;
}

.oh-payslip__employee-detail-title {
  position: relative;
  color: hsl(0, 0%, 45%);
  display: block;
  min-width: 180px;
  margin-right: 1rem;
}
.oh-payslip__employee-detail-title::after {
  content: ": ";
  position: absolute;
  color: hsl(0, 0%, 45%);
  font-size: 0.9rem;
  right: 0;
}

.oh-payslip__summary {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
@media (max-width: 991.98px) {
  .oh-payslip__summary {
    flex-direction: column-reverse;
  }
}

.oh-payslip__netpay {
  text-align: right;
  width: 300px;
  padding: 1rem;
}
@media (max-width: 991.98px) {
  .oh-payslip__netpay {
    width: 100%;
    text-align: left;
    padding-left: 0;
    padding-right: 0;
    margin-bottom: 1.5rem;
  }
}

.oh-payslip__netpay-title {
  color: hsl(0, 0%, 37%);
  display: block;
  margin-bottom: 0.25rem;
}

.oh-payslip__netpay-amount {
  font-size: 2rem;
}

.oh-payslip__table-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 1.5rem;
}
@media (max-width: 991.98px) {
  .oh-payslip__table-container {
    flex-direction: column;
  }
}

@media (max-width: 991.98px) {
  .oh-payslip__user-detail {
    margin-bottom: 1rem;
  }
}

.oh-payslip__table {
  width: 100%;
  border: 1px solid hsl(213, 22%, 93%);
  margin-right: 1rem;
}
.oh-payslip__table:last-child {
  margin-right: 0;
}
@media (max-width: 991.98px) {
  .oh-payslip__table {
    margin-bottom: 1rem;
  }
}

.oh-payslip__table-th,
.oh-payslip__table-td,
.oh-payslip__table-tf {
  padding: 0.75rem;
}

.oh-payslip__table-th {
  border-bottom: 1px solid hsl(213, 22%, 93%);
}

.oh-payslip__net-payable {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 1.25rem;
  border: 1px solid hsl(213, 22%, 93%);
  height: 75px;
}
@media (max-width: 991.98px) {
  .oh-payslip__net-payable {
    flex-direction: column;
    height: auto;
  }
}

.oh-payslip__net-payable-title {
  font-size: 1.15rem;
  font-weight: bold;
  line-height: 1rem;
}

.oh-payslip__net-payable-subtext {
  font-size: 0.95rem;
  color: hsl(0, 0%, 45%);
  margin-bottom: 0;
}

.oh-payslip__net-payable-amount {
  font-size: 1.15rem;
  font-weight: bold;
}

.oh-payslip__net-payable-left,
.oh-payslip__net-payable-right {
  padding: 1rem;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.oh-payslip__net-payable-right {
  background-color: hsl(213, 22%, 93%);
  min-width: 200px;
}
@media (max-width: 991.98px) {
  .oh-payslip__net-payable-right {
    width: 100%;
  }
}

.oh-payslip__net-payable-left {
  flex-direction: column;
  align-items: flex-start;
}

.oh-payslip__net-payable-subtext {
  font-size: 0.85rem;
}

.oh-payroll__component-title {
  font-size: 1.35rem;
  font-weight: bold;
}

body.login-page {
  background-color: #f8f9fa !important;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.oh-auth {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 2rem 1rem;
}
@media (max-width: 767.98px) {
  .oh-auth {
    padding: 1rem;
  }
}

.oh-auth-card {
  background-color: white;
  width: 100%;
  max-width: 420px;
  padding: 3rem 2.5rem 2.5rem 2.5rem;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 3px 6px rgba(0, 0, 0, 0.04);
  border: none;
}
@media (max-width: 767.98px) {
  .oh-auth-card {
    padding: 2rem 1.5rem;
    max-width: 350px;
  }
}

.login-title {
  font-size: 2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  text-align: center;
  letter-spacing: -0.025em;
}

.login-subtitle {
  color: #6c757d;
  font-size: 1rem;
  text-align: center;
  margin-bottom: 2.5rem;
  font-weight: 400;
  line-height: 1.4;
}

.login-form-group {
  margin-bottom: 1.5rem;
}

.login-label {
  display: block;
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.login-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 1px solid #e0e6ed;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
  transition: all 0.2s ease-in-out;
  background-color: #fff;
}
.login-input:focus {
  outline: none;
  border-color: #4dabf7;
  box-shadow: 0 0 0 3px rgba(77, 171, 247, 0.1);
}
.login-input::-moz-placeholder {
  color: #adb5bd;
  font-weight: 400;
}
.login-input::placeholder {
  color: #adb5bd;
  font-weight: 400;
}

.login-password-container {
  position: relative;
}
.login-password-container .login-password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: color 0.2s ease;
}
.login-password-container .login-password-toggle:hover {
  color: #495057;
}
.login-password-container .login-password-toggle:focus {
  outline: none;
  color: #495057;
}
.login-password-container .login-password-toggle ion-icon {
  font-size: 1.25rem;
}

.login-submit-btn {
  width: 100%;
  padding: 1rem;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}
.login-submit-btn:hover {
  background-color: #c0392b;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}
.login-submit-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.25);
}
.login-submit-btn:active {
  transform: translateY(0);
}
.login-submit-btn ion-icon {
  font-size: 1.1rem;
}

.login-note {
  text-align: center;
  font-size: 0.9rem;
  color: #007bff;
  margin-bottom: 1.25rem;
  line-height: 1.5;
  font-weight: 400;
}

.login-forgot-link {
  display: block;
  text-align: center;
  color: #e74c3c;
  font-size: 0.95rem;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}
.login-forgot-link:hover {
  color: #c0392b;
  text-decoration: underline;
}
.login-forgot-link:focus {
  outline: none;
  color: #c0392b;
  text-decoration: underline;
}

.login-logo {
  margin-top: 3rem;
  text-align: center;
}
.login-logo img {
  max-width: 100px;
  opacity: 0.4;
  filter: grayscale(100%);
}

.login-company-name {
  font-size: 1.1rem;
  color: #6c757d;
  text-align: center;
  margin-top: 0.75rem;
  margin-bottom: 0;
  font-weight: 300;
}

.oh-alert-container {
  position: fixed;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  width: 90%;
  max-width: 400px;
}

.oh-alert {
  padding: 0.875rem 1rem;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}
.oh-alert.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
.oh-alert.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}
.oh-alert.warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

@media (max-width: 480px) {
  .oh-auth-card {
    padding: 1.5rem 1rem;
    margin: 1rem 0.5rem;
  }
  .login-title {
    font-size: 1.75rem;
  }
  .login-subtitle {
    font-size: 0.9rem;
    margin-bottom: 2rem;
  }
  .login-input {
    padding: 0.75rem 0.875rem;
    font-size: 0.95rem;
  }
  .login-submit-btn {
    padding: 0.875rem;
    font-size: 0.95rem;
  }
  .login-note {
    font-size: 0.85rem;
  }
  .login-forgot-link {
    font-size: 0.9rem;
  }
}
@media (min-width: 768px) and (max-width: 1024px) {
  .oh-auth-card {
    max-width: 450px;
    padding: 3.5rem 3rem 3rem 3rem;
  }
}
@media (min-width: 1025px) {
  .oh-auth-card {
    max-width: 480px;
    padding: 4rem 3.5rem 3.5rem 3.5rem;
  }
  .login-title {
    font-size: 2.25rem;
  }
  .login-subtitle {
    font-size: 1.05rem;
  }
}
.oh-404 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
@media screen and (max-width: 576px) {
  .oh-404 {
    width: 80%;
  }
}

.oh-404__image {
  width: 80px;
  height: 80px;
}

.oh-404__title {
  font-size: 1.75rem;
  font-weight: bold;
  margin-top: 1rem;
  text-align: center;
}
@media screen and (max-width: 576px) {
  .oh-404__title {
    font-size: 1.25rem;
  }
}

.oh-404__subtitle {
  color: hsl(0, 0%, 45%);
  text-align: center;
}
@media screen and (max-width: 576px) {
  .oh-404__subtitle {
    font-size: 0.9rem;
  }
}

.oh-we-calendar__row {
  display: flex;
}
.oh-we-calendar__row .oh-we-calendar__cell {
  border-right: none;
}
.oh-we-calendar__row .oh-we-calendar__cell:last-child {
  border-right: 1px solid hsl(213, 22%, 84%);
}

.oh-we-calendar__header {
  display: flex;
}

.oh-we-calendar__header-cell {
  width: 40px;
  text-align: center;
  font-weight: bold;
}

.oh-we-calendar--week .oh-we-calendar__header-cell.oh-we-calendar__header-cell--title, .oh-we-calendar--week .oh-we-calendar__cell.oh-we-calendar__cell--name {
  width: 300px;
}

.oh-we-calendar--week .oh-we-calendar__header-cell, .oh-we-calendar--week .oh-we-calendar__cell {
  width: 140px;
}

.oh-we-calendar__header-cell--title {
  padding: 10px !important;
  text-align: left;
}

.oh-we-calendar__header-cell {
  padding: 10px 0;
}

.oh-we-calendar__cell {
  border: 1px solid hsl(213, 22%, 84%);
  width: 40px;
  min-height: 50px;
}

.oh-we-calendar__cell--name {
  padding: 10px !important;
}

.oh-we-calendar__cell {
  padding: 2px;
}

.oh-we-calendar__cell--name, .oh-we-calendar__header-cell--title {
  width: 300px;
}

@media (max-width: 991.98px) {
  .oh-chat > .oh-wrapper {
    max-width: 100%;
  }
}

.oh-chat-container {
  height: 100vh;
  overflow-y: hidden;
}

.oh-chat__header-profile {
  display: flex;
  align-items: center;
}

.oh-chat__header-avatar {
  width: 48px;
  height: 48px;
}

.oh-chat__header {
  background-color: hsl(0, 0%, 100%);
  padding: 1rem 0;
  border-bottom: 1px solid hsl(213, 22%, 93%);
}
.oh-chat__header::before {
  content: "";
  position: fixed;
  width: 50%;
  height: 100%;
  background: hsl(0, 0%, 100%);
  left: 0;
  top: 65px;
  max-width: 500px;
  z-index: -1;
}
.oh-chat__header::after {
  content: "";
  background: white;
  border-bottom: 1px solid hsl(213, 22%, 93%);
  width: 100vw;
  height: 71.08px;
  position: absolute;
  display: block;
  z-index: -2;
  top: 65px;
  left: 0;
}
@media (max-width: 991.98px) {
  .oh-chat__header {
    position: sticky;
    top: 0;
    width: 100%;
    z-index: 98;
    background-color: hsl(0, 0%, 100%);
  }
  .oh-chat__header::after {
    content: unset;
  }
  .oh-chat__header::before {
    content: unset;
  }
}

.oh-chat__header-name,
.oh-chat__header-status {
  display: block;
}

.oh-chat__header-name {
  font-weight: bold;
}

.oh-chat__header-status {
  color: hsl(0, 0%, 45%);
  font-size: 0.75rem;
}

.oh-chat__content {
  display: grid;
  grid-template-columns: 300px auto;
}
@media screen and (max-width: 991.98px) {
  .oh-chat__content {
    display: unset;
  }
}

.oh-chat__sidebar {
  background-color: hsl(0, 0%, 100%);
  border-right: 1px solid hsl(213, 22%, 93%);
  height: 100vh;
  padding-right: 8%;
  z-index: 2;
}
@media (max-width: 991.98px) {
  .oh-chat__sidebar {
    display: none;
    position: fixed;
    width: 90%;
    top: 70px;
    z-index: 99;
    padding-left: 1rem;
  }
}

@media (max-width: 991.98px) {
  .oh-chat__sidebar--show {
    display: block;
  }
}

.oh-chat__sidebar {
  padding-top: 1.5rem;
}

.oh-chat__main {
  width: 100%;
  background-color: hsl(0, 0%, 97.5%);
}

.oh-chat__main-chatter {
  height: calc(100vh - 222px);
  overflow-y: auto;
  display: flex;
  flex-direction: column-reverse;
}
@media (max-width: 991.98px) {
  .oh-chat__main-chatter {
    width: 100vw;
    height: calc(100vh - 216px);
  }
}

.oh-chat__main-input-container {
  background-color: hsl(0, 0%, 100%);
  border-top: 1px solid hsl(213, 22%, 93%);
  height: 95px;
  width: 100%;
  padding: 1em 0;
  position: sticky;
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: 0;
  left: auto;
  display: flex;
  flex-direction: column;
}
.oh-chat__main-input-container::after {
  content: "";
  background-color: hsl(0, 0%, 100%);
  border-top: 1px solid hsl(213, 22%, 93%);
  height: 95px;
  width: 100vw;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: -1;
}

.oh-chat__sidebar-search-input {
  position: relative;
  background-color: hsl(0, 0%, 96%);
  border: none;
  padding: 1em 1em 1em 1.85rem;
  width: 100%;
  font-size: 0.75rem;
}
.oh-chat__sidebar-search-input:focus, .oh-chat__sidebar-search-input:focus-visible {
  outline: hsl(213, 22%, 84%) solid 1px;
}

.oh-chat__sidebar-search-container {
  position: relative;
}
.oh-chat__sidebar-search-container::before {
  content: "";
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzkzYTBiNCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2Utd2lkdGg9IjIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwLjM2NCAzYTcuMzY0IDcuMzY0IDAgMSAwIDAgMTQuNzI3IDcuMzY0IDcuMzY0IDAgMCAwIDAtMTQuNzI3djBaIj48L3BhdGg+CjxwYXRoIGQ9Ik0xNS44NTcgMTUuODYgMjEgMjEuMDAxIj48L3BhdGg+Cjwvc3ZnPg==");
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  display: block;
  position: absolute;
  width: 14px;
  height: 14px;
  z-index: 1;
  top: 52.5%;
  left: 0.7rem;
  transform: translateY(-60%);
}

.oh-chat__sidebar-section {
  height: 50%;
}

.oh-chat__sidebar-sections {
  height: calc(100% - 80px);
  margin-top: 1em;
}

.oh-chat__sidebar-section-header {
  border-bottom: 1px solid hsl(213, 22%, 93%);
  padding: 0.5rem 0;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.oh-chat__sidebar-section-title {
  font-size: 1.15rem;
  font-weight: bold;
  margin-bottom: 0;
}

.oh-chat__sidebar-users {
  list-style-type: none;
  padding-left: 0;
  display: block;
}

.oh-chat__sidebar-user {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  margin-bottom: 1.5rem;
}
.oh-chat__sidebar-user:last-child {
  margin-bottom: 0;
}
.oh-chat__sidebar-user:hover .oh-chat__sidebar-username {
  color: hsl(8, 77%, 56%);
  transition: color 0.3s ease-in-out;
}
.oh-chat__sidebar-user:hover img {
  opacity: 0.7;
  transition: all 0.3s ease-in-out;
}

.oh-chat__sidebar-username {
  font-weight: bold;
}

.oh-chat__sidebar-usertext {
  margin-bottom: 0;
  color: hsl(0, 0%, 45%);
  font-size: 0.9em;
}

.oh-chat__sidebar-username,
.oh-chat__sidebar-usertext {
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 185px;
  white-space: nowrap;
}

.oh-chat-sidebar__timestamp {
  color: hsl(0, 0%, 45%);
  font-size: 0.75rem;
  position: absolute;
  top: 5px;
  right: 0;
}

.oh-chat__main-chatter::-webkit-scrollbar,
.oh-chat__sidebar-section-body::-webkit-scrollbar,
.oh-chat__add-group-users-container::-webkit-scrollbar,
.oh-chat__selected-user-contianer::-webkit-scrollbar {
  visibility: hidden;
  width: 5px;
}
.oh-chat__main-chatter:hover,
.oh-chat__sidebar-section-body:hover,
.oh-chat__add-group-users-container:hover,
.oh-chat__selected-user-contianer:hover {
  /* width */
  /* Track */
  /* Handle */
  /* Handle on hover */
}
.oh-chat__main-chatter:hover::-webkit-scrollbar,
.oh-chat__sidebar-section-body:hover::-webkit-scrollbar,
.oh-chat__add-group-users-container:hover::-webkit-scrollbar,
.oh-chat__selected-user-contianer:hover::-webkit-scrollbar {
  visibility: visible;
}
.oh-chat__main-chatter:hover::-webkit-scrollbar-track,
.oh-chat__sidebar-section-body:hover::-webkit-scrollbar-track,
.oh-chat__add-group-users-container:hover::-webkit-scrollbar-track,
.oh-chat__selected-user-contianer:hover::-webkit-scrollbar-track {
  background: rgba(233.223, 236.7573, 241.077, 0.3);
  border-radius: 25px;
}
.oh-chat__main-chatter:hover::-webkit-scrollbar-thumb,
.oh-chat__sidebar-section-body:hover::-webkit-scrollbar-thumb,
.oh-chat__add-group-users-container:hover::-webkit-scrollbar-thumb,
.oh-chat__selected-user-contianer:hover::-webkit-scrollbar-thumb {
  background: hsl(213, 22%, 84%);
  border-radius: 25px;
}
.oh-chat__main-chatter:hover::-webkit-scrollbar-thumb:hover,
.oh-chat__sidebar-section-body:hover::-webkit-scrollbar-thumb:hover,
.oh-chat__add-group-users-container:hover::-webkit-scrollbar-thumb:hover,
.oh-chat__selected-user-contianer:hover::-webkit-scrollbar-thumb:hover {
  background: hsl(216, 18%, 64%);
}

.oh-chat__sidebar-section-body {
  height: calc(100% - 63px);
  overflow-y: auto;
}

.oh-chat__sidebar-section {
  position: relative;
}
.oh-chat__sidebar-section::after {
  content: "";
  background: rgb(255, 255, 255);
  background: linear-gradient(0deg, rgb(255, 255, 255) 0%, rgba(255, 255, 255, 0) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#ffffff",endColorstr="#ffffff",GradientType=1);
  position: absolute;
  display: block;
  width: 100%;
  height: 30px;
  pointer-events: none;
  bottom: 0;
  left: 0;
}

.oh-chat__header-avatar {
  width: 38px;
  height: 38px;
}

.oh-chat__header-avatar-status {
  position: relative;
}
.oh-chat__header-avatar-status::after {
  content: "";
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: block;
  outline: hsl(0, 0%, 100%) solid 1px;
  border: 1px solid hsl(213, 22%, 93%);
  bottom: 0px;
  right: 0px;
  position: absolute;
}

.oh-chat__header-avatar-status--online::after {
  border-color: hsl(148, 70%, 40%);
  background-color: hsl(148, 71%, 44%);
}

.oh-chat__header-avatar-status--offline::after {
  background-color: hsl(0, 0%, 100%);
}

.oh-chat__main-input {
  width: 100%;
  font-size: 0.9rem;
  padding-top: 0.6rem;
  padding-bottom: 0.6rem;
  padding-left: 2.5rem;
  background-color: rgba(233.223, 236.7573, 241.077, 0.3);
  border: none;
  margin-bottom: 0.35rem;
}
.oh-chat__main-input:focus, .oh-chat__main-input:focus-visible {
  outline: 1px solid hsl(213, 22%, 84%);
}
@media (max-width: 991.98px) {
  .oh-chat__main-input {
    width: 100%;
  }
}

.oh-chat__input-wrapper {
  display: flex;
  align-items: center;
}

.oh-chat__bubble {
  position: relative;
  display: flex;
  margin-bottom: 2rem;
  transform: translateZ(0);
}
.oh-chat__bubble:last-child {
  margin-bottom: 0;
}
.oh-chat__bubble .oh-chat__bubble-content {
  max-width: 80%;
  display: inline-block;
  position: relative;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: rgba(0, 0, 0, 0.08) -4px 9px 25px -6px;
}

.oh-chat__bubble--other .oh-chat__bubble-content {
  background-color: hsl(0, 0%, 100%);
  color: hsl(0, 0%, 11%);
  margin-top: 3rem;
  border: 1px solid hsl(213, 22%, 84%);
}

.oh-chat__bubble--other + .oh-chat__bubble--other .oh-chat__bubble-user-info {
  display: none;
}
.oh-chat__bubble--other + .oh-chat__bubble--other .oh-chat__bubble-content {
  margin-top: 1rem;
}

.oh-chat__bubble--you {
  justify-content: flex-end;
}
.oh-chat__bubble--you .oh-chat__bubble-content {
  background-color: hsl(8, 77%, 56%);
  color: hsl(0, 0%, 100%);
  margin-left: auto;
  border: 1px solid hsl(8, 61%, 50%);
}

.oh-chat__bubble-timestamp {
  position: absolute;
  font-size: 0.7rem;
  color: rgba(28.05, 28.05, 28.05, 0.5);
  min-width: 100px;
  right: 0px;
  bottom: -20px;
  text-align: right;
}

.oh-chat__header-icons {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.oh-chat--sidebar-toggle-btn {
  padding: 0;
  font-size: 24px !important;
  margin-right: 0.5rem;
}
@media (min-width: 992px) {
  .oh-chat--sidebar-toggle-btn {
    display: none;
  }
}

.oh-chat__sidebar-close-btn {
  padding: 0;
  font-size: 24px !important;
  margin-bottom: 1rem;
}
@media (min-width: 992px) {
  .oh-chat__sidebar-close-btn {
    display: none;
  }
}

.oh-chat__bubble-user-info {
  display: flex;
  align-items: center;
  position: absolute;
  top: -35px;
  left: 0;
}

.oh-chat__bubble-user {
  font-size: 0.7rem;
  font-weight: bold;
  max-width: 250px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.oh-chat__bubble-avatar {
  width: 24px;
  height: 24px;
}

.oh-chat__bubble-content--unread {
  position: relative;
}
.oh-chat__bubble-content--unread::after {
  content: "";
  width: 8px;
  height: 8px;
  display: inline-block;
  position: absolute;
  border-radius: 50%;
  background-color: hsl(1, 64%, 49%);
  top: -25px;
  right: 0;
}

.oh-chat__typing {
  display: flex;
  align-items: center;
}

.oh-chat__typing-message {
  color: hsl(0, 0%, 37%);
  font-size: 0.65rem;
  margin-top: 0.4rem;
}

@keyframes typingAnimation {
  0% {
    top: 1px;
    background-color: rgba(94.35, 94.35, 94.35, 0.4);
  }
  50% {
    top: -1px;
    background-color: rgba(94.35, 94.35, 94.35, 0.6);
  }
  100% {
    top: 1px;
    background-color: rgba(94.35, 94.35, 94.35, 0.4);
  }
}
.oh-chat__typing-animation {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: relative;
  display: inline-block;
  background-color: rgba(94.35, 94.35, 94.35, 0.4);
  animation: typingAnimation 0.8s ease-in-out 1s infinite;
  top: 3px;
  left: 8px;
  margin-right: 1.5rem;
  transition: all 0.3s ease-in-out;
}
.oh-chat__typing-animation::after, .oh-chat__typing-animation::before {
  content: "";
  position: absolute;
  width: inherit;
  height: inherit;
  border-radius: inherit;
  background-color: inherit;
  display: inherit;
  transition: inherit;
}
.oh-chat__typing-animation::before {
  left: -8px;
  animation: typingAnimation 0.8s ease-in-out 0 infinite;
}
.oh-chat__typing-animation::after {
  right: -8px;
  animation: typingAnimation 0.8s ease-in-out 2s infinite;
}

.oh-chat__empty {
  height: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
}
@media (max-width: 991.98px) {
  .oh-chat__empty {
    width: 100vw;
  }
}

.oh-chat__empty-message {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
@media (max-width: 991.98px) {
  .oh-chat__empty-message {
    position: relative;
    top: 100%;
  }
}

.oh-chat__empty-image {
  width: 150px;
  height: auto;
  margin: 0 auto;
  -o-object-fit: contain;
     object-fit: contain;
}

.oh-chat__empty-title {
  font-size: 1.15rem;
  font-weight: bold;
  text-align: center;
  line-height: 1rem;
  margin-top: 1rem;
}

.oh-chat__empty-subtitle {
  color: hsl(0, 0%, 45%);
  text-align: center;
}

.oh-chat-sidebar__unread-badge {
  border-radius: 25px;
  padding: 0.25rem 0.5rem;
  min-width: 35px;
  text-align: center;
  display: inline-block;
  font-size: 0.65rem;
  color: hsl(0, 0%, 100%);
  background-color: hsl(1, 64%, 49%);
  position: absolute;
  top: 25px;
  right: 0;
}

.oh-chat__bubble-timestamp--read {
  padding-right: 1rem;
}
.oh-chat__bubble-timestamp--read::after {
  content: "";
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIGZpbGw9Im5vbmUiIHN0cm9rZT0iI2M0YzRjNCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBzdHJva2Utd2lkdGg9IjIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0ibTIxLjc1IDYtMTAuNSAxMi00LjUtNC41Ij48L3BhdGg+CjxwYXRoIGQ9Im02Ljc1IDE4LTQuNS00LjUiPjwvcGF0aD4KPHBhdGggZD0ibTE3LjI1IDYtNi4zNzUgNy4zMTMiPjwvcGF0aD4KPC9zdmc+");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  width: 14px;
  height: 14px;
  position: absolute;
  right: 0;
}

.oh-chat__dropdown-dialog {
  position: absolute;
  background-color: hsl(0, 0%, 100%);
  border: 1px solid hsl(213, 22%, 93%);
  padding: 0.5rem;
  min-width: 300px;
  z-index: 99;
  top: -80px;
  left: 25px;
  transform: translateY(-25%);
  box-shadow: rgba(0, 0, 0, 0.08) -4px 9px 25px -6px;
}
@media screen and (max-width: 991.98px) {
  .oh-chat__dropdown-dialog {
    left: -250px;
  }
}

.oh-chat__add-group-users {
  padding-left: 0;
}

.oh-chat__dropdown-dialog-footer {
  padding-top: 0.5rem;
}

.oh-chat__add-group-user {
  display: flex;
  align-items: center;
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  cursor: pointer;
  transition: all 0.4s ease-in-out;
}
.oh-chat__add-group-user:hover, .oh-chat__add-group-user:focus, .oh-chat__add-group-user:focus-visible {
  color: hsl(8, 77%, 56%);
  transition: all 0.4s ease-in-out;
}
.oh-chat__add-group-user:hover img, .oh-chat__add-group-user:focus img, .oh-chat__add-group-user:focus-visible img {
  opacity: 0.7;
  transition: all 0.4s ease-in-out;
}

.oh-chat__add-group-users-container {
  height: 250px;
  overflow-y: auto;
}

.oh-chat__dialog-members-title {
  font-size: 0.9rem;
  text-transform: uppercase;
  font-weight: bold;
  margin-top: 1.05rem;
  margin-bottom: 0.5rem;
}

.oh-chat__selected-user-contianer {
  margin-top: 0.5rem;
  max-height: 90px;
  overflow-y: auto;
  display: flex;
  flex-direction: column-reverse;
}

.oh-chat__selected-users {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.oh-chat__member-tag {
  display: inline-flex;
  align-items: center;
  border-radius: 25px;
  border: 1px solid hsl(213, 22%, 93%);
  padding: 0.35rem;
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
  transition: all 0.4s ease-in-out;
  cursor: pointer;
}
.oh-chat__member-tag:hover {
  background-color: rgba(233.223, 236.7573, 241.077, 0.6);
  border-color: hsl(213, 22%, 84%);
  transition: all 0.4s ease-in-out;
}

.oh-chat__member-tag-name {
  font-size: 0.8rem;
  border-radius: 50px;
  font-weight: bold;
}

.oh-chat__member-tag-image {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  -o-object-fit: contain;
     object-fit: contain;
  margin-right: 0.3rem;
}

.oh-chat__bottom-deck {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 40px;
}

.oh-chat__file-tag {
  font-size: 0.8rem;
  display: inline-flex;
  align-items: center;
  font-weight: bold;
  border: 1px solid hsl(213, 22%, 93%);
  padding: 0.25rem;
  padding-left: 0.45rem;
  border-radius: 50px;
  transition: all 0.4s ease-in-out;
}
.oh-chat__file-tag:hover {
  background-color: hsl(0, 0%, 97.5%);
  transition: all 0.4s ease-in-out;
}

.oh-chat__file-remove-btn {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIGZpbGw9IiM1MjUyNTIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMTIgMi4yNWMtNS4zNzYgMC05Ljc1IDQuMzc0LTkuNzUgOS43NXM0LjM3NCA5Ljc1IDkuNzUgOS43NSA5Ljc1LTQuMzc0IDkuNzUtOS43NVMxNy4zNzYgMi4yNSAxMiAyLjI1Wm0zLjUzIDEyLjIyYS43NS43NSAwIDEgMS0xLjA2IDEuMDZMMTIgMTMuMDZsLTIuNDcgMi40N2EuNzUuNzUgMCAwIDEtMS4wNi0xLjA2TDEwLjk0IDEyIDguNDcgOS41M2EuNzUuNzUgMCAwIDEgMS4wNi0xLjA2TDEyIDEwLjk0bDIuNDctMi40N2EuNzUuNzUgMCAwIDEgMS4wNiAxLjA2TDEzLjA2IDEybDIuNDcgMi40N1oiPjwvcGF0aD4KPC9zdmc+");
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  background-color: transparent;
  border: none;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  opacity: 0.6;
  cursor: pointer;
  transition: all 0.5s ease-in-out;
}
.oh-chat__file-remove-btn:hover {
  opacity: 1;
  cursor: pointer;
  transition: all 0.5s ease-in-out;
}

.oh-chat__file-label {
  margin-right: 0.15rem;
  max-width: 150px;
  white-space: nowrap;
  overflow-x: hidden;
  text-overflow: ellipsis;
}

.oh-chat__file-icon {
  width: 14px;
  height: 14px;
  -o-object-fit: contain;
     object-fit: contain;
  margin-right: 0.25rem;
}

.oh-chat__files > span {
  margin-right: 0.5rem;
}
.oh-chat__files > span:last-child {
  margin-right: 0;
}

.oh-helpdesk {
  display: grid;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
  grid-template-columns: 70% 30%;
}
@media screen and (max-width: 991.98px) {
  .oh-helpdesk {
    display: flex;
    flex-direction: column-reverse;
  }
}

.oh-helpdesk__header {
  background-color: hsl(0, 0%, 100%);
  border-bottom: 1px solid hsl(213, 22%, 93%);
  padding-left: 3.75%;
  padding-top: 20px;
  padding-bottom: 20px;
}
@media screen and (max-width: 991.98px) {
  .oh-helpdesk__header {
    display: none;
  }
}

.oh-helpdesk__header-title {
  font-weight: 600;
  font-size: 1.07rem;
  margin-bottom: 0.15rem;
}

.oh-helpdesk__header-status {
  display: flex;
  align-items: center;
  margin-top: 0.25rem;
}

.oh-helpdesk__status-text {
  font-size: 0.75rem;
  margin-left: 5px;
}

.oh-helpdesk__timestamp {
  font-size: 0.75rem;
}

.oh-helpdesk__meta-items {
  display: flex;
  align-items: center;
  gap: 15px;
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
}

.oh-helpdesk__meta-item {
  position: relative;
}
.oh-helpdesk__meta-item::after {
  content: "";
  position: absolute;
  top: 3px;
  right: -8px;
  background-color: hsl(213, 22%, 84%);
  width: 1px;
  height: 18px;
  display: block;
}
.oh-helpdesk__meta-item:last-child::after {
  content: unset;
}

.oh-helpdesk__timestamp,
.oh-helpdesk__header-status {
  color: hsl(216, 3%, 39%);
}

.oh-helpdesk__right {
  height: calc(100vh - 65px);
  background-color: hsl(0, 0%, 100%);
  overflow-y: auto;
  border-left: 1px solid hsl(213, 22%, 93%);
  padding: 0.5rem;
}
@media screen and (max-width: 991.98px) {
  .oh-helpdesk__right {
    height: unset;
    border-left: 0;
  }
}

.oh-helpdesk__card {
  background-color: hsl(0, 0%, 100%);
  border: 1px solid hsl(213, 22%, 93%);
  box-shadow: rgba(17, 17, 26, 0.1) 0px 1px 0px;
  margin-bottom: 0.75rem;
}
.oh-helpdesk__card:last-child {
  margin-bottom: 0;
}

.oh-helpdesk__card-header,
.oh-helpdesk__card-body {
  padding: 1rem;
}

.oh-helpdesk__card-header {
  padding-top: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid hsl(213, 22%, 93%);
}

.oh-helpdesk__card-body {
  padding-bottom: 1.5rem;
}

.oh-helpdesk__card-title {
  font-size: 1.05rem;
  font-weight: bold;
}

.oh-helpdesk__subcard {
  margin-bottom: 1.5rem;
}
.oh-helpdesk__subcard:last-child {
  margin-bottom: 0;
}

.oh-helpdesk__subcard-title {
  font-weight: bold;
}

.oh-helpdesk__subcard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 5px;
  margin-bottom: 1rem;
}

.oh-helpdesk__subcard-items {
  padding-left: 0;
  list-style: none;
  margin-bottom: 0;
}

.oh-helpdesk__body {
  height: calc(100vh - 225.97px);
  display: flex;
  flex-direction: column-reverse;
  padding-left: 3.75%;
  padding-right: 3.75%;
  overflow-y: auto;
}

.oh-helpdesk__chat-container {
  display: flex;
  flex-direction: column;
}

.oh-helpdesk__chatbox {
  width: 100%;
  background-color: hsl(0, 0%, 100%);
  border-top: 1px solid hsl(213, 22%, 93%);
  padding: 1rem 3.75%;
  box-shadow: rgba(0, 0, 0, 0.06) 0px -1px 6px 0px, rgba(0, 0, 0, 0.03) 0px -1px 5px 0px;
}

.oh-helpdesk__chat-input {
  resize: none;
  max-height: 55px;
  padding-top: 17px;
}
.oh-helpdesk__chat-input::-moz-placeholder {
  position: relative;
  top: 2px;
}
.oh-helpdesk__chat-input::placeholder {
  position: relative;
  top: 2px;
}

.oh-chat {
  display: grid;
  grid-template-columns: 98% 2%;
}

.oh-helpdesk__bubble {
  background-color: hsl(0, 0%, 100%);
  border: 1px solid hsl(213, 22%, 84%);
  padding: 1rem;
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px 0px;
}

.oh-helpdesk__bubble-username {
  font-weight: bold;
}

.oh-helpdesk__bubble-header {
  padding-top: 0.5rem;
  padding-bottom: 0.75rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid hsl(213, 22%, 93%);
}

.oh-helpdesk__bubble-timestamp {
  font-size: 0.85rem;
  opacity: 0.5;
}

.oh-helpdesk__bubble--reply {
  border-color: hsl(8, 77%, 56%);
  background-color: rgba(229.194, 79.4444, 56.406, 0.03);
}
.oh-helpdesk__bubble--reply .oh-helpdesk__bubble-header {
  border-color: hsl(213, 22%, 84%);
}

.oh-helpdesk__bubble--sender {
  border-color: hsl(225, 72%, 48%);
  background-color: rgba(235.722, 244.1948, 253.878, 0.5);
}
.oh-helpdesk__bubble--sender .oh-helpdesk__bubble-header {
  border-color: hsl(213, 22%, 84%);
}

.oh-helpdesk__chat-update {
  font-size: 0.85rem;
  color: hsl(0, 0%, 45%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
  width: 100%;
}

.helpdesk__card-items {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
}

.helpdesk__card-item {
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
}
.helpdesk__card-item:last-child {
  margin-bottom: 0;
}

.helpdesk__card-label {
  opacity: 0.6;
  display: inline-block;
  margin-right: 0.3rem;
}

.oh-helpdesk__documents {
  list-style: none;
  padding-left: 0;
}

.oh-helpdesk__icon {
  min-width: 42px;
  width: 42px;
  min-height: 42px;
  height: 42px;
  border-radius: 5px;
  background-color: hsl(0, 0%, 96%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.oh-file-icon {
  display: block;
  width: 24px;
  height: 24px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.oh-file-icon--pdf {
  background-image: url("data:image/svg+xml;base64,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");
}

.oh-file-icon--image {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHdpZHRoPSI1MTIiIGhlaWdodD0iNTEyIiB4PSIwIiB5PSIwIiB2aWV3Qm94PSIwIDAgMjQgMjQiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDUxMiA1MTIiIHhtbDpzcGFjZT0icHJlc2VydmUiIGNsYXNzPSIiPjxnPjxnIGZpbGw9IiMwMDAiPjxwYXRoIGQ9Ik02LjUgOWEyLjUgMi41IDAgMSAwIDAtNSAyLjUgMi41IDAgMCAwIDAgNXpNMTQgN2wtNS4yMjMgOC40ODdMNyAxM2wtNSA3aDIweiIgZmlsbD0iI2U1NGYzOSIgb3BhY2l0eT0iMSIgZGF0YS1vcmlnaW5hbD0iIzAwMDAwMCIgY2xhc3M9IiI+PC9wYXRoPjwvZz48L2c+PC9zdmc+");
}

.oh-file-icon--audio {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHdpZHRoPSI1MTIiIGhlaWdodD0iNTEyIiB4PSIwIiB5PSIwIiB2aWV3Qm94PSIwIDAgNjQgNjQiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDUxMiA1MTIiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxnPjxwYXRoIGQ9Ik03LjcyIDI0LjgzQTMuNzI2IDMuNzI2IDAgMCAwIDQgMjguNTV2Ni45YTMuNzE1IDMuNzE1IDAgMSAwIDcuNDMgMHYtNi45YTMuNzE3IDMuNzE3IDAgMCAwLTMuNzEtMy43MnpNMTkuODYgMTlhMy43MjYgMy43MjYgMCAwIDAtMy43MiAzLjcydjE4LjU2YTMuNzE1IDMuNzE1IDAgMSAwIDcuNDMgMFYyMi43MkEzLjcxNyAzLjcxNyAwIDAgMCAxOS44NiAxOXpNMzIgNS4wNGEzLjcyNiAzLjcyNiAwIDAgMC0zLjcyIDMuNzJ2NDYuNDhhMy43MiAzLjcyIDAgMSAwIDcuNDQgMFY4Ljc2QTMuNzI2IDMuNzI2IDAgMCAwIDMyIDUuMDR6TTQ0LjE0IDExLjkyYTMuNzE3IDMuNzE3IDAgMCAwLTMuNzEgMy43MnYzMi43MmEzLjcxNSAzLjcxNSAwIDEgMCA3LjQzIDBWMTUuNjRhMy43MjYgMy43MjYgMCAwIDAtMy43Mi0zLjcyek01Ni4yOCAxNy45OWEzLjcxNyAzLjcxNyAwIDAgMC0zLjcxIDMuNzJ2MjAuNThhMy43MTUgMy43MTUgMCAxIDAgNy40MyAwVjIxLjcxYTMuNzI2IDMuNzI2IDAgMCAwLTMuNzItMy43MnoiIGZpbGw9IiNlNTRmMzkiIG9wYWNpdHk9IjEiIGRhdGEtb3JpZ2luYWw9IiMwMDAwMDAiPjwvcGF0aD48L2c+PC9zdmc+");
}

.oh-file-icon--file {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHdpZHRoPSI1MTIiIGhlaWdodD0iNTEyIiB4PSIwIiB5PSIwIiB2aWV3Qm94PSIwIDAgMzIgMzIiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDUxMiA1MTIiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxnPjxwYXRoIGQ9Ik0yMiA4VjIuNjNMMjcuNDYgOUgyM2ExIDEgMCAwIDEtMS0xem0xIDNhMyAzIDAgMCAxLTMtM1YySDdhMyAzIDAgMCAwLTMgM3YyMmEzIDMgMCAwIDAgMyAzaDE4YTMgMyAwIDAgMCAzLTNWMTF6IiBkYXRhLW5hbWU9IkxheWVyIDEwIiBmaWxsPSIjZTU0ZjM5IiBvcGFjaXR5PSIxIiBkYXRhLW9yaWdpbmFsPSIjMDAwMDAwIj48L3BhdGg+PC9nPjwvc3ZnPg==");
}

.oh-helpdesk__documents {
  margin-bottom: 0;
}

.oh-helpdesk__document {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-top: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid hsl(213, 22%, 93%);
  transition: all 0.3s linear;
  cursor: pointer;
}
.oh-helpdesk__document:hover .oh-helpdesk__icon {
  background-color: rgba(205.224, 213.3024, 223.176, 0.6);
  transition: all 0.3s linear;
}
.oh-helpdesk__document:first-child {
  padding-top: 0;
}
.oh-helpdesk__document:last-child {
  padding-bottom: 0px;
  border-bottom: none;
}

.oh-helpdesk__filename {
  width: 100%;
  white-space: nowrap;
  overflow-x: hidden;
  text-overflow: ellipsis;
}

.helpdesk__card-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid hsl(213, 22%, 93%);
}

.oh-helpdesk__tags {
  padding-left: 0;
  margin-top: 0.75rem;
  margin-bottom: 0;
  list-style: none;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 5px;
}

.oh-helpdesk__tag {
  display: inline-block;
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  background-color: hsl(213, 22%, 93%);
  color: hsl(0, 0%, 27%);
  transition: all 0.3s linear;
}
.oh-helpdesk__tag:hover {
  background-color: rgba(205.224, 213.3024, 223.176, 0.6);
  transition: all 0.3s linear;
}

.oh-helpdesk__close {
  border: none;
  background-color: transparent;
  padding-left: 0;
  color: hsl(0, 0%, 27%);
}

.oh-helpdesk__right-header {
  display: none;
  padding: 1rem;
  font-weight: bold;
  font-size: 1.15rem;
  position: relative;
}
.oh-helpdesk__right-header:after {
  content: "";
  width: 22px;
  height: 22px;
  top: 18px;
  right: 0;
  position: absolute;
  display: inline-block;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='none' d='M0 0h24v24H0z'%3E%3C/path%3E%3Cpath d='M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z' fill='rgba(28,28,28,1)'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s linear;
}
.oh-helpdesk__right-header.oh-helpdesk__right-header--active::after {
  transform: rotate(180deg);
  transition: all 0.3s linear;
}
@media screen and (max-width: 991.98px) {
  .oh-helpdesk__right-header {
    display: flex;
  }
}

@media screen and (max-width: 991.98px) {
  .oh-helpdesk__right-body {
    display: none;
  }
}

.oh-helpdesk__right-body--show {
  display: block;
}

@media screen and (max-width: 991.98px) {
  .oh-helpdesk__chatbox .oh-chat {
    grid-template-columns: 93% 2%;
  }
}

.oh-helpdesk__body::-webkit-scrollbar,
.oh-helpdesk__right::-webkit-scrollbar {
  visibility: hidden;
  width: 5px;
}
.oh-helpdesk__body:hover,
.oh-helpdesk__right:hover {
  /* width */
  /* Track */
  /* Handle */
  /* Handle on hover */
}
.oh-helpdesk__body:hover::-webkit-scrollbar,
.oh-helpdesk__right:hover::-webkit-scrollbar {
  visibility: visible;
}
.oh-helpdesk__body:hover::-webkit-scrollbar-track,
.oh-helpdesk__right:hover::-webkit-scrollbar-track {
  background: rgba(233.223, 236.7573, 241.077, 0.3);
  border-radius: 25px;
}
.oh-helpdesk__body:hover::-webkit-scrollbar-thumb,
.oh-helpdesk__right:hover::-webkit-scrollbar-thumb {
  background: hsl(213, 22%, 84%);
  border-radius: 25px;
}
.oh-helpdesk__body:hover::-webkit-scrollbar-thumb:hover,
.oh-helpdesk__right:hover::-webkit-scrollbar-thumb:hover {
  background: hsl(216, 18%, 64%);
}

.oh-helpdesk-selectbox {
  margin-bottom: 1rem;
}

.oh-helpdesk_selection .select2-container--default .select2-selection--single .select2-selection__rendered {
  font-weight: bold;
}

.oh-helpdesk__bubble--comment {
  width: -moz-fit-content;
  width: fit-content;
}

.oh-helpdesk__comment--container {
  padding-left: 3rem;
  border-left: 1px solid hsl(213, 22%, 84%);
  margin-left: 1rem;
}

.oh-helpdesk__comment {
  line-height: 1.7rem;
}

.oh-helpdesk__bubble-description {
  height: 300px;
  overflow: auto;
}

.oh-helpdesk__attachment-icon {
  min-width: 42px;
  width: 42px;
  min-height: 42px;
  height: 42px;
  border-radius: 5px;
  background-color: rgba(229.194, 79.4444, 56.406, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.7rem;
}

.oh-helpdesk_attached-items {
  background-color: hsl(0, 0%, 97.5%);
  border: 2px solid hsl(213, 22%, 93%);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.4rem 0 0.4rem 0.75rem;
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  min-width: 50%;
  width: -moz-fit-content;
  width: fit-content;
}
.oh-helpdesk_attached-items:hover {
  background-color: hsl(0, 0%, 96%);
}

.oh-helpdesk_attached--content {
  display: flex;
  align-items: center;
}

.oh-faq {
  background-color: hsl(0, 0%, 100%);
  border: 1px solid hsl(213, 22%, 93%);
}

.oh-faq__items {
  padding-left: 0;
  list-style: none;
}

.oh-faq__item {
  background-color: hsl(0, 0%, 100%);
  padding: 1.5rem;
  padding-bottom: 0;
}

.oh-faq__item-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding-bottom: 1rem;
  border-bottom: 1px solid hsl(213, 22%, 93%);
}

.oh-faq__item-title {
  font-weight: bold;
  display: block;
}

.oh-faq__item-body {
  border-bottom: 1px solid hsl(213, 22%, 93%);
}

.oh-faq__item.oh-faq__item--show:last-child .oh-faq__item-body {
  border-bottom: none;
}

.oh-faq__tags {
  padding-left: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.65rem;
  margin-top: 0.5rem;
}

.oh-faq__tag {
  padding: 0.3rem 1rem;
  background-color: hsl(213, 22%, 93%);
  cursor: pointer;
}
.oh-faq__tag:hover {
  background-color: hsl(0, 0%, 97.5%);
}

.oh-faq__item-header__right {
  display: flex;
  align-items: flex-start;
  gap: 5px;
}

.oh-faq__item--show .oh-faq__item-header {
  border-bottom: none;
}
.oh-faq__item--show .oh-faq__item-body {
  max-height: 200px;
}

.oh-faq__item:not(.oh-faq__item--show):last-child .oh-faq__item-header {
  border-bottom: none;
}

.oh-faq__item-body {
  max-height: 0px;
}

.oh-faq-cards {
  display: grid;
  grid-template-columns: repeat(4, minmax(250px, 1fr));
  gap: 15px;
}

@media (max-width: 768px) {
  .oh-faq-cards {
    grid-template-columns: repeat(1, 1fr);
  }
}
@media (max-width: 1316px) {
  .oh-faq-cards {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}
.oh-faq-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.oh-faq-card {
  background-color: hsl(0, 0%, 100%);
  border: 1px solid hsl(213, 22%, 93%);
  padding: 1.5rem;
  transition: all 0.3s linear;
}
.oh-faq-card:hover {
  border: 1px solid hsl(213, 22%, 84%);
  transition: all 0.3s linear;
  box-shadow: rgba(0, 0, 0, 0.04) 0px 3px 5px;
}

.oh-faq-card__title {
  font-size: 1.15rem;
  font-weight: bold;
  margin-bottom: 0.75rem;
}

.oh-faq-card__desc {
  color: hsl(0, 0%, 45%);
  min-height: 60px;
  margin-bottom: 1.25rem;
  max-height: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Custom Styles */
.oh-faq__item-body {
  border-bottom: 1px solid hsl(213, 22%, 93%);
}

.oh-faq__item.oh-faq__item--show:last-child .oh-faq__item-body {
  border-bottom: none;
}

.oh-faq__tags {
  padding-left: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.65rem;
  margin-top: 0.5rem;
}

.oh-faq__tag {
  padding: 0.3rem 1rem;
  background-color: hsl(213, 22%, 93%);
  cursor: pointer;
}

.oh-faq__tag:hover {
  background-color: hsl(0, 0%, 97.5%);
}

.oh-faq__item-header__right {
  display: flex;
  align-items: flex-start;
  gap: 5px;
}

.oh-faq__item--show .oh-faq__item-header {
  border-bottom: none;
}

.oh-faq__item--show .oh-faq__item-body {
  padding: 1rem;
  max-height: 200px;
}

.oh-faq__item:not(.oh-faq__item--show):last-child .oh-faq__item-header {
  border-bottom: none;
}

.oh-faq__item-body {
  max-height: 0px;
}

.oh-faq__input-search {
  width: 100%;
  margin: 1rem 0 2rem 0;
}

.oh-search_input {
  width: 100%;
}

.oh-faq_search--icon {
  position: absolute;
  top: 15px;
  left: 10px;
}

.oh-autocomplete-suggestions {
  position: absolute;
  list-style: none;
  margin: 0;
  background-color: hsl(0, 0%, 100%);
  max-height: 150px;
  overflow-y: auto;
  width: 100%;
  display: none;
  border: 1px solid hsl(213, 22%, 93%);
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
  z-index: 9999;
}

.oh-select-faq {
  width: 35%;
  margin: 1rem 0 2rem 0;
  height: 48px;
  padding: 0.8rem;
}
.oh-select-faq:last-child {
  margin: 1rem 0 2rem 0;
}

.autocomplete-suggestion {
  padding: 1rem;
  cursor: pointer;
}
.autocomplete-suggestion:hover {
  background-color: hsl(0, 0%, 97.5%);
}

.oh-title_faq__main-header {
  gap: 1rem;
}

.oh-survey__table-row {
  display: flex;
  border-bottom: 1px solid hsl(213, 22%, 93%);
}
.oh-survey__table-row:last-child {
  border-bottom: none;
}

.oh-survey__table-row:hover {
  border-bottom: 1px solid hsl(8, 77%, 56%);
}
.oh-survey__table-row:hover .oh-survey__table-col {
  background-color: hsl(0, 0%, 96%);
}

.oh-survey__table-col {
  width: 100%;
  min-height: 45px;
  padding: 25px 25px 25px 10px;
  line-height: 1.75rem;
}
.oh-survey__table-col:last-child {
  color: hsl(0, 0%, 45%);
}

.drag-handle {
  width: 1.2rem;
  height: auto;
  margin-right: 0.2rem;
}
.drag-handle img {
  width: 100%;
}

.oh-survey-ques .oh-card {
  margin-top: 1rem;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.12);
  border: none;
  border-radius: 6px;
  cursor: grabbing;
  padding-left: 0.9rem;
}
.oh-survey-ques .oh-label--question {
  margin-top: 0px;
  margin-bottom: 1rem;
}
.oh-survey-ques .oh-rate {
  height: auto;
}
.oh-survey-ques .oh-input {
  border: 1px solid hsl(213, 22%, 93%);
  border-radius: 4px;
}
.oh-survey-ques .select2-container--default .select2-selection--multiple {
  border: 1px solid hsl(213, 22%, 93%) !important;
  border-radius: 4px !important;
}

.oh-survey_page {
  background-color: hsla(7.98, 76.89%, 55.88%, 0.04);
}

.oh-badge_count {
  background-color: hsl(8, 77%, 56%);
  color: white;
  border-radius: 30px;
  min-width: 20px;
  min-height: 20px;
  padding: 0.2rem 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: -moz-fit-content;
  width: fit-content;
}

.oh-badge--small {
  margin-top: 0px !important;
  min-width: 20px !important;
  min-height: 20px !important;
  display: flex !important;
  font-size: 0.7rem !important;
  align-items: center !important;
  /* line-height: 0; */
  width: -moz-fit-content;
  width: fit-content;
  justify-content: center !important;
  padding: 0.1rem 0.5rem;
  height: -moz-fit-content;
  height: fit-content;
  border-radius: 30px;
}

.oh-job__page-container {
  max-width: 80%;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.oh-joblisting__title-container {
  margin: 3rem 0;
}

.oh-jobs__container--list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1%;
  row-gap: 1rem;
  justify-content: space-between;
  padding-top: 5rem;
}

.oh-joblist__header {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.oh-job_recruit--detail {
  display: flex;
}

.oh-recruitment-company {
  width: 64px;
  height: 64px;
  border-radius: 6px;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  padding: 0.5rem;
  margin-right: 0.8rem;
}

.oh-job-list_company-logo {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}

.oh-recuritment_company-name {
  color: hsl(0, 0%, 11%);
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.oh-joblist_card {
  flex-direction: column;
  align-items: flex-start;
}

.oh-job__post {
  font-weight: 600;
  font-size: 1.1rem;
  margin: 1rem 0;
}

.oh-job_post--posteddate {
  font-size: 0.8rem;
}

.oh-recuritment__progress-bar {
  width: 100%;
  height: 8px;
  border-radius: 30px;
  margin: 0.75rem 0;
  background-color: hsl(213, 22%, 93%);
  position: relative;
}

.oh-progress-bar__state {
  background-color: hsl(8, 77%, 56%);
  position: absolute;
  left: 0px;
  height: 8px;
  border-radius: 30px;
}

.oh-recuritment_application-count {
  color: hsl(0, 0%, 11%);
}

.oh-recuritment_application {
  color: hsl(0, 0%, 45%);
  font-size: 0.8rem;
}

.oh-job__apply-btn {
  display: flex;
  justify-content: end;
  width: 100%;
  margin-top: 1rem;
}

.oh-apply_btn {
  font-weight: 500;
  border: 1px solid hsl(8, 77%, 56%);
  background-color: hsl(0, 0%, 97.5%);
  color: hsl(8, 77%, 56%);
  text-decoration: none;
}
.oh-apply_btn:active {
  background-color: hsl(8, 77%, 56%);
  color: hsl(0, 0%, 100%);
}
.oh-apply_btn:focus {
  background-color: hsl(8, 77%, 56%);
  color: hsl(0, 0%, 100%);
  border: none;
  outline: none;
}

.oh-apply_btn:hover {
  background-color: hsl(8, 77%, 56%);
  color: hsl(0, 0%, 100%);
  text-decoration: none;
}

.oh-recuritment_tag {
  background-color: hsl(0, 75%, 97%);
  color: hsl(1, 100%, 61%);
  padding: 0.4rem 0.6rem;
  border-radius: 0.2rem;
  margin-right: 0.2rem;
  font-size: 0.8rem;
  height: -moz-fit-content;
  height: fit-content;
}

.oh-job-selected_detail {
  position: fixed;
  right: 0px;
  width: 400px;
  background-color: hsl(0, 0%, 100%);
  top: 0px;
  height: 100%;
}
@media (max-width: 575.98px) {
  .oh-job-selected_detail {
    width: 100%;
  }
}

.oh-job-selected__detail--container {
  padding: 2.5rem 1.5rem;
}

.oh-job__post_description {
  line-height: 1.8rem;
}

.oh-job__description-list .oh-job__description-list_item {
  line-height: 1.8rem;
  color: hsl(0, 0%, 11%);
  position: relative;
  list-style: none;
}
.oh-job__description-list .oh-job__description-list_item::before {
  content: "";
  position: absolute;
  display: block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  left: -26px;
  background-color: hsl(8, 77%, 56%);
  top: 10px;
}

.oh-announcement_feed-card {
  background-color: hsl(0, 0%, 100%);
  padding: 1.3rem;
  margin: 0.75rem auto;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px 0px;
  max-width: 75%;
}

.oh-announcement-headline {
  font-weight: bold;
}

.oh-annpouncement_profile--name {
  font-size: 1.2rem;
}

.oh-announcement-timing {
  font-size: 0.85rem;
  opacity: 0.8;
  margin-top: 0.2rem;
}

.oh-announcement_header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid hsl(213, 22%, 93%);
}

.oh-announcement-body {
  margin: 1rem 0;
}

.oh-announcement_text {
  line-height: 1.6rem;
}

.oh-announcement-hastag__container {
  margin: 1rem 0;
}

.oh-announcement-hashtags {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  flex-wrap: wrap;
  margin-top: 0.2rem;
}

.oh-announcement__tags {
  color: hsl(0, 0%, 45%);
  font-size: 0.8rem;
  font-style: italic;
}

.oh-announcement-img_container {
  width: 100%;
  height: 400px;
  border-radius: 4px;
  overflow: hidden;
}

.oh-posted_img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.oh-announcement__comment-view {
  display: flex;
  align-items: center;
  justify-content: end;
  margin-top: 0.5rem;
}

.oh-announcement_attachmenent {
  border: none;
  padding: 0.5rem 0 0.5rem 0.75rem;
}

.oh-announcement-btn:hover {
  color: hsl(8, 77%, 56%);
}

@media (max-width: 768px) {
  .oh-announcement_topbar {
    flex-direction: row;
  }
}
.qs-datepicker-container {
  border: 1px solid hsl(213, 22%, 93%) !important;
  font-family: "Inter", sans-serif !important;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px !important;
}

.qs-controls {
  background: transparent !important;
}

.qs-square.qs-day {
  font-family: "Inter", sans-serif !important;
  font-size: 0.8rem !important;
  font-weight: 400 !important;
}

.qs-square {
  height: 35px !important;
  font-size: 0.85rem !important;
}

.qs-square:not(.qs-empty):not(.qs-disabled):not(.qs-day):not(.qs-active):hover {
  background-color: rgba(233.223, 236.7573, 241.077, 0.6) !important;
}

.qs-square.qs-num.qs-current {
  text-decoration: none !important;
  color: hsl(8, 77%, 56%) !important;
}

.qs-square.qs-num.qs-active {
  background-color: rgba(229.194, 79.4444, 56.406, 0.2) !important;
}

.qs-month-year {
  border-bottom: none !important;
}

.qs-month-year:not(.qs-disabled-year-overlay):hover {
  border-bottom: none !important;
}

.qs-arrow.qs-right {
  position: absolute !important;
  right: 8px !important;
}

.qs-arrow.qs-left {
  position: absolute !important;
  right: 25px !important;
}

.qs-controls {
  padding: 25px 0px 10px 15px;
}

.qs-arrow:hover {
  background: transparent !important;
}

.qs-month, .qs-year {
  font-weight: 400;
  font-size: 1.15rem;
}

.qs-range-start {
  background-color: rgb(233.223, 236.7573, 241.077) !important;
}

.qs-range-middle {
  background-color: rgba(233.223, 236.7573, 241.077, 0.6) !important;
}

.qs-overlay {
  background: hsl(0, 0%, 100%) !important;
  background-color: hsl(0, 0%, 100%) !important;
  color: hsl(0, 0%, 11%) !important;
}

.qs-overlay-month {
  color: hsl(0, 0%, 11%) !important;
  opacity: 1 !important;
}
.qs-overlay-month:hover {
  background-color: rgba(229.194, 79.4444, 56.406, 0.1) !important;
  color: hsl(8, 77%, 56%) !important;
}

.qs-overlay-year {
  color: hsl(0, 0%, 11%) !important;
  border-radius: 0px !important;
}
.qs-overlay-year:focus, .qs-overlay-year:focus-visible {
  outline: hsl(0, 0%, 13%) solid 2px !important;
}

.qs-overlay .qs-submit {
  width: 100% !important;
  border: none !important;
  background-color: hsl(8, 77%, 56%) !important;
  color: hsl(0, 0%, 100%) !important;
  text-align: center !important;
  border-radius: 0 !important;
}
.qs-overlay .qs-submit:hover {
  background-color: hsl(8, 61%, 50%);
}

.qs-overlay .qs-submit.qs-disabled {
  color: rgba(28.05, 28.05, 28.05, 0.8) !important;
  background-color: rgba(233.223, 236.7573, 241.077, 0.5) !important;
}

.qs-overlay-month-container {
  margin-bottom: 0.25rem;
}

.fc .fc-button {
  border-radius: 0 !important;
}

.fc .fc-button-primary {
  background-color: hsl(0, 0%, 13%) !important;
  border-color: hsl(0, 0%, 12%) !important;
}

.fc .fc-button-primary.fc-button-active {
  background-color: rgba(33.15, 33.15, 33.15, 0.7) !important;
  border-color: rgba(33.15, 33.15, 33.15, 0.7) !important;
}
.fc .fc-button-primary.fc-button-active:focus, .fc .fc-button-primary.fc-button-active:focus-visible {
  outline: none;
  border: none;
  box-shadow: none;
}

.fc-event {
  border-radius: 0 !important;
}

@media (max-width: 679px) {
  .fc .fc-toolbar.fc-header-toolbar {
    display: grid;
    grid-template-columns: auto 1fr;
    justify-content: space-between;
    gap: 5px;
  }
  .fc .fc-toolbar-title {
    font-size: 1.25rem;
    text-align: right;
  }
}
@media (max-width: 767.98px) {
  .oh-main__titlebar-button-container .oh-btn-group.ml-2 {
    margin-left: 0 !important;
  }
  .oh-view-types.ml-2 {
    margin-left: 0;
    margin-right: 10px;
  }
  .oh-btn-group.ml-2:last-child {
    margin-left: 0 !important;
  }
  .oh-main__titlebar-button-container {
    margin-top: 0 !important;
  }
  .oh-main__titlebar--right {
    width: unset;
  }
  .oh-dropdown__filter {
    z-index: 9999;
    left: 50%;
    position: fixed;
    width: 80%;
    transform: translateX(-50%);
  }
}
@media (max-width: 575.98px) {
  .oh-dropdown__filter {
    width: 95%;
  }
  .attendance-activity-container button {
    padding: 0 !important;
    border: none !important;
  }
  .attendance-activity-container button .hr-check-in-out-text {
    display: none;
  }
  .oh-navbar__clock-icon {
    font-size: 1.35rem !important;
  }
  .oh-tabs__movable-title {
    display: none;
  }
  .oh-view-types.ml-2 {
    margin-left: 0;
  }
  .oh-main__titlebar-button-container {
    margin-top: 0.5rem;
  }
  .oh-main__titlebar--right {
    flex-direction: column;
    align-items: flex-start;
  }
  .oh-btn-group.ml-2 {
    margin-left: 0;
  }
  .oh-main__titlebar-button-container .oh-btn.oh-btn--shadow {
    box-shadow: none !important;
  }
}
.note-modal-backdrop {
  z-index: 1020 !important;
}

.fullscreen .note-editing-area {
  background-color: hsl(0, 0%, 100%);
  height: 100vh !important;
}
