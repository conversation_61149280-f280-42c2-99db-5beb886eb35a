"""
Bulk Update Methods for Employee Data

This module contains methods for processing bulk updates of employee data
including file processing, validation, preview generation, and update execution.
"""

import logging
import pandas as pd
import re
from datetime import datetime
from decimal import Decimal, InvalidOperation
from django.db import transaction
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext as _

from employee.models import (
    <PERSON>ployee, 
    EmployeeWorkInformation, 
    EmployeeBankDetails,
    EmployeeNigerianCompliance, 
    EmployeeExtendedContract,
    BulkUpdateOperation,
    BulkUpdatePreview,
    BulkUpdateRecord
)
from base.models import Department, JobPosition, JobRole, WorkType, EmployeeType, Company

logger = logging.getLogger(__name__)


class BulkUpdateProcessor:
    """
    Main class for processing bulk updates
    """
    
    def __init__(self, operation_id):
        self.operation = BulkUpdateOperation.objects.get(id=operation_id)
        self.errors = []
        self.warnings = []
        
    def process_file(self, file_obj, employee_identifier='email', update_fields=None):
        """
        Process uploaded file and create preview data
        """
        try:
            # Read file based on extension
            file_extension = file_obj.name.split('.')[-1].lower()
            
            if file_extension == 'csv':
                df = pd.read_csv(file_obj)
            elif file_extension in ['xlsx', 'xls']:
                df = pd.read_excel(file_obj)
            else:
                raise ValueError(_("Unsupported file format"))
            
            # Clean column names
            df.columns = df.columns.str.strip()
            
            # Validate required columns
            if employee_identifier not in df.columns:
                raise ValueError(_(f"Required identifier column '{employee_identifier}' not found in file"))
            
            # Update operation with total records
            self.operation.total_records = len(df)
            self.operation.save()
            
            # Process each row
            preview_data = []
            validation_errors = []
            validation_warnings = []
            
            for index, row in df.iterrows():
                try:
                    processed_row = self.process_row(
                        row, 
                        index + 1, 
                        employee_identifier, 
                        update_fields
                    )
                    preview_data.append(processed_row)
                except Exception as e:
                    error_info = {
                        'row': index + 1,
                        'error': str(e),
                        'data': row.to_dict()
                    }
                    validation_errors.append(error_info)
            
            # Create preview record
            preview = BulkUpdatePreview.objects.create(
                operation=self.operation,
                preview_data=preview_data,
                validation_errors=validation_errors,
                validation_warnings=validation_warnings,
                total_employees_to_update=len([p for p in preview_data if p.get('employee_found')]),
                employees_not_found=len([p for p in preview_data if not p.get('employee_found')]),
                duplicate_entries=self.count_duplicates(preview_data, employee_identifier)
            )
            
            return preview
            
        except Exception as e:
            logger.error(f"Error processing file: {e}")
            self.operation.status = 'failed'
            self.operation.error_message = str(e)
            self.operation.save()
            raise
    
    def process_row(self, row, row_number, employee_identifier, update_fields=None):
        """
        Process a single row of data
        """
        processed_row = {
            'row_number': row_number,
            'original_data': row.to_dict(),
            'employee_identifier_value': row.get(employee_identifier),
            'employee_found': False,
            'employee_id': None,
            'employee_name': '',
            'updates': {},
            'errors': [],
            'warnings': []
        }
        
        # Find employee
        employee = self.find_employee(row.get(employee_identifier), employee_identifier)
        if employee:
            processed_row['employee_found'] = True
            processed_row['employee_id'] = employee.id
            processed_row['employee_name'] = f"{employee.employee_first_name} {employee.employee_last_name}"
            
            # Process updates
            updates = self.extract_updates(row, employee, update_fields)
            processed_row['updates'] = updates
        else:
            processed_row['errors'].append(f"Employee not found with {employee_identifier}: {row.get(employee_identifier)}")
        
        return processed_row
    
    def find_employee(self, identifier_value, identifier_type):
        """
        Find employee by identifier
        """
        if not identifier_value or pd.isna(identifier_value):
            return None
            
        try:
            if identifier_type == 'email':
                return Employee.objects.get(email=identifier_value)
            elif identifier_type == 'badge_id':
                return Employee.objects.get(badge_id=identifier_value)
            elif identifier_type == 'employee_id':
                return Employee.objects.get(id=identifier_value)
        except Employee.DoesNotExist:
            return None
        except Employee.MultipleObjectsReturned:
            # Log warning about multiple employees
            logger.warning(f"Multiple employees found for {identifier_type}: {identifier_value}")
            return Employee.objects.filter(**{identifier_type: identifier_value}).first()
        
        return None
    
    def extract_updates(self, row, employee, update_fields=None):
        """
        Extract update data from row
        """
        updates = {
            'employee': {},
            'work_info': {},
            'bank_details': {},
            'nigerian_compliance': {},
            'extended_contract': {}
        }
        
        # Field mappings
        field_mappings = self.get_field_mappings()
        
        for column, value in row.items():
            if pd.isna(value) or value == '':
                continue
                
            # Skip if specific fields are requested and this field is not in the list
            if update_fields and column.lower() not in [f.lower() for f in update_fields]:
                continue
            
            # Find which model this field belongs to
            for model_name, fields in field_mappings.items():
                if column in fields:
                    field_name = fields[column]
                    processed_value = self.process_field_value(field_name, value, model_name)
                    if processed_value is not None:
                        updates[model_name][field_name] = processed_value
                    break
        
        return updates
    
    def get_field_mappings(self):
        """
        Get mappings between CSV columns and model fields
        """
        return {
            'employee': {
                'First Name': 'employee_first_name',
                'Last Name': 'employee_last_name',
                'Phone': 'phone',
                'Gender': 'gender',
                'Date of Birth': 'dob',
                'Country': 'country',
                'State': 'state',
                'City': 'city',
                'Address': 'address',
                'Zip Code': 'zip',
                'Marital Status': 'marital_status',
                'Children': 'children',
                'Emergency Contact': 'emergency_contact',
                'Emergency Contact Name': 'emergency_contact_name',
                'Emergency Contact Relation': 'emergency_contact_relation',
            },
            'work_info': {
                'Basic Salary': 'basic_salary',
                'Salary Hour': 'salary_hour',
                'Date Joining': 'date_joining',
                'Contract End Date': 'contract_end_date',
                'Location': 'location',
                'Department': 'department_id',
                'Job Position': 'job_position_id',
                'Job Role': 'job_role_id',
                'Employee Type': 'employee_type_id',
                'Work Type': 'work_type_id',
                'Shift': 'shift_id',
                'Reporting Manager': 'reporting_manager_id',
                'Company': 'company_id',
            },
            'bank_details': {
                'Bank Name': 'bank_name',
                'Branch': 'branch',
                'Account Number': 'account_number',
                'Bank Code #1': 'any_other_code1',
                'Bank Code #2': 'any_other_code2',
                'Bank Country': 'country',
                'Bank State': 'state',
                'Bank City': 'city',
            },
            'nigerian_compliance': {
                'TIN': 'tax_identification_number',
                'State Taxpayer ID': 'state_tax_payer_id',
                'NIN': 'national_identity_number',
                'BVN': 'bank_verification_number',
                'Voter\'s Card Number': 'voters_card_number',
                'Passport Number': 'international_passport_number',
                'Passport Expiry': 'passport_expiry_date',
                'Driver\'s License': 'drivers_license_number',
                'License Expiry': 'drivers_license_expiry',
                'RSA PIN': 'rsa_pin',
                'PFA Name': 'pfa_name',
                'PFA Code': 'pfa_code',
                'NSITF Number': 'nsitf_number',
                'NHIA Number': 'nhia_number',
                'HMO Provider': 'hmo_provider',
            },
            'extended_contract': {
                'ITF Certificate': 'itf_certificate_number',
                'NHF Registration': 'nhf_registration_number',
                'NHF Voluntary Status': 'nhf_voluntary_status',
                'Contract Registration': 'contract_registration_number',
                'Probation Period (Months)': 'probation_period_months',
                'Notice Period (Days)': 'notice_period_days',
            }
        }
    
    def process_field_value(self, field_name, value, model_name):
        """
        Process and validate field values
        """
        try:
            # Handle different field types
            if field_name in ['basic_salary', 'salary_hour']:
                return Decimal(str(value))
            elif field_name in ['date_joining', 'contract_end_date', 'passport_expiry_date', 'drivers_license_expiry']:
                return self.parse_date(value)
            elif field_name in ['children', 'probation_period_months', 'notice_period_days']:
                return int(value)
            elif field_name == 'nhf_voluntary_status':
                return str(value).lower() in ['true', 'yes', '1', 'y']
            elif field_name in ['department_id', 'job_position_id', 'job_role_id', 'employee_type_id', 'work_type_id', 'company_id']:
                return self.resolve_foreign_key(field_name, value)
            else:
                return str(value).strip()
        except Exception as e:
            logger.warning(f"Error processing field {field_name} with value {value}: {e}")
            return None
    
    def parse_date(self, date_value):
        """
        Parse date from various formats
        """
        if pd.isna(date_value):
            return None
            
        if isinstance(date_value, datetime):
            return date_value.date()
        
        # Try different date formats
        date_formats = ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y']
        
        for fmt in date_formats:
            try:
                return datetime.strptime(str(date_value), fmt).date()
            except ValueError:
                continue
        
        raise ValueError(f"Unable to parse date: {date_value}")
    
    def resolve_foreign_key(self, field_name, value):
        """
        Resolve foreign key references
        """
        model_map = {
            'department_id': Department,
            'job_position_id': JobPosition,
            'job_role_id': JobRole,
            'employee_type_id': EmployeeType,
            'work_type_id': WorkType,
            'company_id': Company,
        }
        
        if field_name not in model_map:
            return value
        
        model = model_map[field_name]
        
        # Try to find by name or ID
        try:
            if str(value).isdigit():
                return model.objects.get(id=int(value))
            else:
                # Try to find by name field
                name_field = 'department' if field_name == 'department_id' else field_name.replace('_id', '')
                return model.objects.get(**{name_field: value})
        except model.DoesNotExist:
            raise ValueError(f"{model.__name__} '{value}' not found")
    
    def count_duplicates(self, preview_data, identifier_field):
        """
        Count duplicate entries in the data
        """
        identifiers = [p['employee_identifier_value'] for p in preview_data if p['employee_identifier_value']]
        return len(identifiers) - len(set(identifiers))

    def execute_updates(self):
        """
        Execute the bulk updates based on preview data
        """
        try:
            self.operation.status = 'processing'
            self.operation.started_at = timezone.now()
            self.operation.save()

            preview = self.operation.preview
            successful_updates = 0
            failed_updates = 0

            with transaction.atomic():
                for row_data in preview.preview_data:
                    if not row_data.get('employee_found'):
                        continue

                    try:
                        employee = Employee.objects.get(id=row_data['employee_id'])
                        self.update_employee_data(employee, row_data['updates'])

                        # Create success record
                        BulkUpdateRecord.objects.create(
                            operation=self.operation,
                            employee=employee,
                            employee_identifier=row_data['employee_identifier_value'],
                            row_number=row_data['row_number'],
                            status='success',
                            original_data=row_data['original_data'],
                            processed_data=row_data['updates'],
                            processed_at=timezone.now()
                        )

                        successful_updates += 1

                    except Exception as e:
                        # Create failure record
                        BulkUpdateRecord.objects.create(
                            operation=self.operation,
                            employee_id=row_data.get('employee_id'),
                            employee_identifier=row_data['employee_identifier_value'],
                            row_number=row_data['row_number'],
                            status='failed',
                            original_data=row_data['original_data'],
                            error_message=str(e),
                            error_details={'exception': str(e)},
                            processed_at=timezone.now()
                        )

                        failed_updates += 1
                        logger.error(f"Error updating employee {row_data.get('employee_id')}: {e}")

                    # Update progress
                    processed = successful_updates + failed_updates
                    self.operation.update_progress(
                        processed=processed,
                        successful=successful_updates,
                        failed=failed_updates
                    )

            # Mark operation as completed
            self.operation.status = 'completed'
            self.operation.completed_at = timezone.now()
            self.operation.successful_updates = successful_updates
            self.operation.failed_updates = failed_updates
            self.operation.save()

            return {
                'successful': successful_updates,
                'failed': failed_updates,
                'total': successful_updates + failed_updates
            }

        except Exception as e:
            self.operation.status = 'failed'
            self.operation.error_message = str(e)
            self.operation.completed_at = timezone.now()
            self.operation.save()
            logger.error(f"Bulk update operation failed: {e}")
            raise

    def update_employee_data(self, employee, updates):
        """
        Update employee data across all related models
        """
        # Update Employee model
        if updates.get('employee'):
            for field, value in updates['employee'].items():
                setattr(employee, field, value)
            employee.save()

        # Update Work Information
        if updates.get('work_info'):
            work_info, created = EmployeeWorkInformation.objects.get_or_create(
                employee_id=employee
            )
            for field, value in updates['work_info'].items():
                setattr(work_info, field, value)
            work_info.save()

        # Update Bank Details
        if updates.get('bank_details'):
            bank_details, created = EmployeeBankDetails.objects.get_or_create(
                employee_id=employee
            )
            for field, value in updates['bank_details'].items():
                setattr(bank_details, field, value)
            bank_details.save()

        # Update Nigerian Compliance
        if updates.get('nigerian_compliance'):
            compliance, created = EmployeeNigerianCompliance.objects.get_or_create(
                employee_id=employee
            )
            for field, value in updates['nigerian_compliance'].items():
                setattr(compliance, field, value)
            compliance.save()

        # Update Extended Contract
        if updates.get('extended_contract'):
            contract, created = EmployeeExtendedContract.objects.get_or_create(
                employee_id=employee
            )
            for field, value in updates['extended_contract'].items():
                setattr(contract, field, value)
            contract.save()


class BulkUpdateTemplateGenerator:
    """
    Generate templates for bulk updates
    """

    @staticmethod
    def generate_template(operation_type, identifier_type='email'):
        """
        Generate template based on operation type
        """
        columns = [identifier_type.title()]

        if operation_type == 'employee_update':
            columns.extend([
                'First Name', 'Last Name', 'Phone', 'Gender', 'Date of Birth',
                'Country', 'State', 'City', 'Address', 'Zip Code',
                'Marital Status', 'Children', 'Emergency Contact',
                'Emergency Contact Name', 'Emergency Contact Relation'
            ])

        elif operation_type == 'work_info_update':
            columns.extend([
                'Basic Salary', 'Salary Hour', 'Date Joining', 'Contract End Date',
                'Location', 'Department', 'Job Position', 'Job Role',
                'Employee Type', 'Work Type', 'Shift', 'Reporting Manager', 'Company'
            ])

        elif operation_type == 'compliance_update':
            columns.extend([
                'TIN', 'State Taxpayer ID', 'NIN', 'BVN', 'Voter\'s Card Number',
                'Passport Number', 'Passport Expiry', 'Driver\'s License', 'License Expiry',
                'RSA PIN', 'PFA Name', 'PFA Code', 'NSITF Number', 'NHIA Number', 'HMO Provider'
            ])

        elif operation_type == 'contract_update':
            columns.extend([
                'ITF Certificate', 'NHF Registration', 'NHF Voluntary Status',
                'Contract Registration', 'Probation Period (Months)', 'Notice Period (Days)'
            ])

        elif operation_type == 'bank_details_update':
            columns.extend([
                'Bank Name', 'Branch', 'Account Number', 'Bank Code #1', 'Bank Code #2',
                'Bank Country', 'Bank State', 'Bank City'
            ])

        elif operation_type == 'mixed_update':
            # Include all possible fields
            columns.extend([
                # Employee fields
                'First Name', 'Last Name', 'Phone', 'Gender', 'Date of Birth',
                'Country', 'State', 'City', 'Address', 'Zip Code',
                'Marital Status', 'Children', 'Emergency Contact',
                'Emergency Contact Name', 'Emergency Contact Relation',
                # Work info fields
                'Basic Salary', 'Salary Hour', 'Date Joining', 'Contract End Date',
                'Location', 'Department', 'Job Position', 'Job Role',
                'Employee Type', 'Work Type', 'Shift', 'Reporting Manager', 'Company',
                # Bank details fields
                'Bank Name', 'Branch', 'Account Number', 'Bank Code #1', 'Bank Code #2',
                'Bank Country', 'Bank State', 'Bank City',
                # Nigerian Compliance fields
                'TIN', 'State Taxpayer ID', 'NIN', 'BVN', 'Voter\'s Card Number',
                'Passport Number', 'Passport Expiry', 'Driver\'s License', 'License Expiry',
                'RSA PIN', 'PFA Name', 'PFA Code', 'NSITF Number', 'NHIA Number', 'HMO Provider',
                # Extended Contract fields
                'ITF Certificate', 'NHF Registration', 'NHF Voluntary Status',
                'Contract Registration', 'Probation Period (Months)', 'Notice Period (Days)'
            ])

        return pd.DataFrame(columns=columns)
