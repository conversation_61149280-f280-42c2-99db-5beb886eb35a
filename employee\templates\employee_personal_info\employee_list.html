{% include 'filter_tags.html' %}
{% load basefilters %} {% load horillafilters %}
{% load static %} {% load i18n %} {% if messages %}
<div class="oh-alert-container">
  {% for message in messages %}
  <div class="oh-alert oh-alert--animated {{message.tags}}">{{ message }}</div>
  {% endfor %}
</div>
{% endif %}
<span name="" id="filterEmployee" style="display: none" hx-get="{% url 'employee-filter-view' %}?{{pd}}" hx-target="#view-container"></span>
<div class="d-flex flex-row-reverse p-0 m-2">
  <span class="" onclick="$('[name=working_today]').val('false'); $('[name=working_today]').first().change(); $('.filterButton').click()" style="cursor: pointer;">
    <span class="oh-dot oh-dot--small me-1" style="background-color:rgba(128, 128, 128, 0.482)"></span>
    {% trans "Offline" %}
  </span>
  <span class="me-3" onclick="$('[name=working_today]').val('true'); $('[name=working_today]').first().change(); $('.filterButton').click()" style="cursor: pointer;">
    <span class="oh-dot oh-dot--small me-1" style="background-color:yellowgreen"></span>
    {% trans "Online" %}
  </span>
  {% comment %} <span class="me-3" style="cursor: pointer;">
    <span class="oh-dot oh-dot--small me-1" style="background-color:black"></span>
    {{data.start_count}}-{{data.end_count}} / {{data.paginator.count}}
  </span> {% endcomment %}
</div>
<div id="relatedModel"></div>
{% if perms.employee.view_employee or request.user|is_reportingmanager %}
<div
  class="oh-checkpoint-badge text-success mb-2"
  id="selectAllEmployees"
  style="cursor: pointer"
  data-pd="{{pd}}"
>
  {% trans "Select All Employees" %}
</div>
<div
  class="oh-checkpoint-badge text-secondary mb-2"
  id="unselectAllEmployees"
  style="cursor: pointer;display: none;"
>
  {% trans "Unselect All Employees" %}
</div>
<div
  class="oh-checkpoint-badge text-info mb-2"
  id="exportEmployees"
  style="cursor: pointer;display: none;"
>
  {% trans "Export Employees" %}
</div>
{% endif %}
<div class="oh-checkpoint-badge text-danger mb-2" id="selectedShow"></div>
{% if data %}
<div class="oh-table_sticky--wrapper">
	<div class="oh-sticky-dropdown--header">
		<div class="oh-dropdown" x-data="{open: false}">
		  <button class="oh-sticky-dropdown_btn " @click="open = !open"><ion-icon name="ellipsis-vertical-sharp"
			  role="img" class="md hydrated" aria-label="ellipsis vertical sharp"></ion-icon></button>
		  <div class="oh-dropdown__menu oh-sticky-table_dropdown" x-show="open" @click.outside="open = false">
			<ul class="oh-dropdown__items" id="fieldContainerTable">
			</ul>
		  </div>
		</div>
	  </div>
<div id="employee-table" data-table-name="employee_table">
<div class="oh-sticky-table">
  <div class="oh-sticky-table__table oh-table--sortable">
    <div class="oh-sticky-table__thead">
      <div class="oh-sticky-table__tr">
        <div class="oh-sticky-table__th" style="width: 50px">
          <div class="centered-div">
            <input
              type="checkbox"
              class="oh-input oh-input__checkbox all-employee"
              title='{% trans "Select All" %}'
              id="tick"
            />
          </div>
        </div>
        <div
          class="oh-sticky-table__th {% if request.sort_option.order == '-employee_first_name' %}arrow-up {% elif request.sort_option.order == 'employee_first_name' %}arrow-down {% else %}arrow-up-down {% endif %}"
          hx-target="#view-container"
          hx-get="{% url 'employee-filter-view' %}?{{pd}}&orderby=employee_first_name"
        >
          {% trans "Employee" %}
        </div>
        {% if perms.employee.view_employee %}
        <div class="oh-sticky-table__th" data-cell-index="1" data-cell-title='{% trans "Email" %}'>{% trans "Email" %}</div>
        <div class="oh-sticky-table__th" data-cell-index="2" data-cell-title='{% trans "Phone" %}'>{% trans "Phone" %}</div>
        {% endif %}
        <div
          class="oh-sticky-table__th {% if request.sort_option.order == '-badge_id' %}arrow-up {% elif request.sort_option.order == 'badge_id' %}arrow-down {% else %}arrow-up-down {% endif %}" data-cell-index="3" data-cell-title='{% trans "Badge Id" %}'
          hx-target="#view-container"
          hx-get="{% url 'employee-filter-view' %}?{{pd}}&orderby=badge_id"
        >
          {% trans "Badge Id" %}
        </div>
        <div class="oh-sticky-table__th" data-cell-index="4" data-cell-title='{% trans "Job Position" %}'>{% trans "Job Position" %}</div>
        <div class="oh-sticky-table__th" data-cell-index="5" data-cell-title='{% trans "Department" %}'>{% trans "Department" %}</div>
        <div class="oh-sticky-table__th" data-cell-index="6" data-cell-title='{% trans "Shift" %}'>{% trans "Shift" %}</div>
        <div class="oh-sticky-table__th" data-cell-index="7" data-cell-title='{% trans "Work Type" %}'>{% trans "Work Type" %}</div>
        <div class="oh-sticky-table__th" data-cell-index="8" data-cell-title='{% trans "Job Role" %}'>{% trans "Job Role" %}</div>
        <div
          class="oh-sticky-table__th {% if request.sort_option.order == '-employee_work_info__reporting_manager_id' %}arrow-up {% elif request.sort_option.order == 'employee_work_info__reporting_manager_id' %}arrow-down {% else %}arrow-up-down {% endif %}" data-cell-index="9" data-cell-title='{% trans "Reporting Manager" %}'
          hx-target="#view-container"
          hx-get="{% url 'employee-filter-view' %}?{{pd}}&orderby=employee_work_info__reporting_manager_id"
        >
          {% trans "Reporting Manager" %}
        </div>
        <div class="oh-sticky-table__th" data-cell-index="10" data-cell-title='{% trans "Company" %}'>{% trans "Company" %}</div>
        <div class="oh-sticky-table__th" data-cell-index="11" data-cell-title='{% trans "Work Email" %}'>{% trans "Work Email" %}</div>
        <div
          class="oh-sticky-table__th {% if request.sort_option.order == '-employee_work_info__date_joining' %}arrow-up {% elif request.sort_option.order == 'employee_work_info__date_joining' %}arrow-down {% else %}arrow-up-down {% endif %}" data-cell-index="12" data-cell-title='{% trans "Date of Joining" %}'
          hx-target="#view-container"
          hx-get="{% url 'employee-filter-view' %}?{{pd}}&orderby=employee_work_info__date_joining"
        >
          {% trans "Date of Joining" %}
        </div>
        <div class="oh-sticky-table__th oh-sticky-table__right" style="width:313px">{% trans "Actions" %}</div>
      </div>
    </div>

    <!-- Company Filter Toggle - Integrated with table -->
    {% if request.session.selected_company and request.session.selected_company != "all" and not request.GET.show_all_companies %}
    <div class="oh-alert oh-alert--info mb-2">
        <div class="d-flex justify-content-between align-items-center">
            <span>
                <i class="oh-icon oh-icon--info"></i>
                Showing employees from selected company only.
            </span>
            <a href="?show_all_companies=true&{{ pd }}" class="oh-btn oh-btn--secondary oh-btn--sm">
                Show All Companies
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Debug information (remove in production) -->
    {% if user.is_superuser %}
    <div class="oh-alert oh-alert--info mb-2">
      <strong>Debug Info:</strong>
      Total employees in data: {{ data|length }}
      {% if data.paginator %}
      | Total in paginator: {{ data.paginator.count }}
      | Current page: {{ data.number }}
      {% endif %}
    </div>
    {% endif %}

    {% for emp in data %}
    <div class="oh-sticky-table__tbody ui-sortable">
      <div class="oh-sticky-table__tr ui-sortable-handle">
        <div class="oh-sticky-table__sd">
          <div class="centered-div">
            <input
              type="checkbox"
              id="{{emp.id}}"
              onchange="highlightRow($(this))"
              class="oh-input employee-checkbox oh-input__checkbox all-employee-row"
            />
          </div>
        </div>
        <a class="oh-sticky-table__td" href="{% url 'employee-view-individual' emp.id %}" style="color: inherit; text-decoration: none">
          <div class="d-flex">
            <div class="oh-profile oh-profile--md">
              <div class="oh-profile__avatar mr-1">
                <img
                  src="{{emp.get_avatar}}"
                  class="oh-profile__image"
                  alt="Username"
                />
              </div>
              <span class="oh-profile__name oh-text--dark">{{emp}}</span>
            </div>
          </div>
        </a>
        {% if perms.employee.view_employee %}
        <a data-cell-index="1"
          href="{% url 'employee-view-individual' emp.id %}"
          style="color: inherit; text-decoration: none"
          class="oh-sticky-table__td"
          >{{emp.email}}</a
        >
        <a data-cell-index="2"
          href="{% url 'employee-view-individual' emp.id %}"
          style="color: inherit; text-decoration: none"
          class="oh-sticky-table__td"
          >{{emp.phone}}</a
        >
        {% endif %}
        <a data-cell-index="3"
          href="{% url 'employee-view-individual' emp.id %}"
          style="color: inherit; text-decoration: none"
          class="oh-sticky-table__td"
          >{{emp.badge_id}}</a
        >
        <a data-cell-index="4"
          href="{% url 'employee-view-individual' emp.id %}"
          style="color: inherit; text-decoration: none"
          class="oh-sticky-table__td"
          >{{emp.employee_work_info.job_position_id}}</a
        >
        <a data-cell-index="5"
          href="{% url 'employee-view-individual' emp.id %}"
          style="color: inherit; text-decoration: none"
          class="oh-sticky-table__td"
          >{{emp.employee_work_info.department_id}}</a
        >
        <a data-cell-index="6"
          href="{% url 'employee-view-individual' emp.id %}"
          style="color: inherit; text-decoration: none"
          class="oh-sticky-table__td"
          >{{emp.employee_work_info.shift_id}}</a
        >
        <a data-cell-index="7"
          href="{% url 'employee-view-individual' emp.id %}"
          style="color: inherit; text-decoration: none"
          class="oh-sticky-table__td"
          >{{emp.employee_work_info.work_type_id}}</a
        >
        <a data-cell-index="8"
          href="{% url 'employee-view-individual' emp.id %}"
          style="color: inherit; text-decoration: none"
          class="oh-sticky-table__td"
          >{{emp.employee_work_info.job_role_id}}</a
        >
        <a data-cell-index="9"
          href="{% url 'employee-view-individual' emp.id %}"
          style="color: inherit; text-decoration: none"
          class="oh-sticky-table__td"
          >{{emp.employee_work_info.reporting_manager_id}}</a
        >
        <a data-cell-index="10"
          href="{% url 'employee-view-individual' emp.id %}"
          style="color: inherit; text-decoration: none"
          class="oh-sticky-table__td"
          >{{emp.employee_work_info.company_id}}</a
        >
        <a data-cell-index="11"
          href="{% url 'employee-view-individual' emp.id %}"
          style="color: inherit; text-decoration: none"
          class="oh-sticky-table__td"
          >{{emp.employee_work_info.email}}</a
        >
        <a data-cell-index="12"
          href="{% url 'employee-view-individual' emp.id %}"
          style="color: inherit; text-decoration: none"
          class="oh-sticky-table__td dateformat_changer"
          >{{emp.employee_work_info.date_joining}}</a
        >
        <div
          style="color: inherit; text-decoration: none"
          class="oh-sticky-table__td oh-sticky-table__right"
        >
          <div class="oh-btn-group">

            {% if "leave"|app_installed or "payroll"|app_installed or "attendance"|app_installed %}
              <button
                type="button"
                hx-get="{% url 'export-data-employee' emp.id %}"
                title="{% trans "Export Individual Data" %}"
                hx-target="#export-content"
                hx-swap="innerHTML"
                class="oh-btn oh-btn--light-bkg w-100"
                data-toggle="oh-modal-toggle"
                data-target="#dataExport"
              >
                <ion-icon name="download-outline"></ion-icon>
              </button>
            {% endif %}

            <button
              type="button"
              hx-get="{% url 'send-mail-employee' emp.id %}"
              title="{% trans "Send Mail" %}"
              hx-target="#mail-content"
              hx-swap="innerHTML"
              class="oh-btn oh-btn--light-bkg w-100"
              data-toggle="oh-modal-toggle"
              data-target="#sendMailModal"
            >
              <ion-icon name="mail-open-outline"></ion-icon>
            </button>
            {% if perms.employee.change_employee or request.user|check_manager:emp %}
            <a
              href="{% url 'employee-view-update' emp.id %}"
              class="oh-btn oh-btn--light-bkg w-100"
              title="{% trans 'Edit' %}"
              ><ion-icon name="create-outline"></ion-icon
            ></a>
            {% endif %}
            {% if perms.employee.delete_employee %}
              {% if emp.is_active %}
                <button hx-confirm="{% trans 'Do you want to archive this employee?' %}" hx-post="{% url 'employee-archive' emp.id %}?{{pd}}"
                        hx-target="#relatedModel" class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100" title="{% trans 'Archive' %}">
                  <ion-icon name="archive"></ion-icon>
                </button>
              {% else %}
                <form hx-confirm="{% trans "Do you want to un-archive this employee?" %}"
                      hx-post="{% url 'employee-archive' emp.id %}"
                      hx-target="#relatedModel"
                      style = "width:100%"
                    >
                  {% csrf_token %}
                  <button
                    type="submit"
                    class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
                    title="{% trans 'Un-Archive' %}"
                    >
                    <ion-icon name="archive"></ion-icon>
                  </button>
                </form>
              {% endif %}
            {% endif %}
            {% if perms.employee.delete_employee %}
              <form
                  action="{% url 'employee-delete' emp.id %}?view=list"
                  onsubmit="return confirm('{% trans "All non-active contracts of the employee will also be deleted. Do you want to continue ?" %}')"
                  method="post" style = "width:100%"
                > {% csrf_token %}
                  <button
                    type="submit"
                    class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
                    title="{% trans 'Remove' %}"
                  >
                    <ion-icon name="trash-outline"></ion-icon>
                  </button>
              </form>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>
</div>
</div>
</div>

<div class="oh-pagination">
  <span class="oh-pagination__page">
    {% trans "Page" %} {{ data.number }} {% trans "of" %} {{ data.paginator.num_pages }}.
  </span>
  <nav class="oh-pagination__nav">
    <div class="oh-pagination__input-container me-3">
      <span class="oh-pagination__label me-1">{% trans "Page" %}</span>
      <input
        type="number"
        name="page"
        class="oh-pagination__input"
        value="{{data.number}}"
        hx-get="{% url 'employee-filter-view' %}?{{pd}}"
        hx-target="#view-container"
        min="1"
      />
      <span class="oh-pagination__label"
        >{% trans "of" %} {{data.paginator.num_pages}}</span
      >
    </div>
    <ul class="oh-pagination__items">
      {% if data.has_previous %}
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-target="#view-container"
          hx-get="{% url 'employee-filter-view' %}?{{pd}}&page=1"
          class="oh-pagination__link"
          >{% trans "First" %}</a
        >
      </li>
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-target="#view-container"
          hx-get="{% url 'employee-filter-view' %}?{{pd}}&page={{ data.previous_page_number }}"
          class="oh-pagination__link"
          >{% trans "Previous" %}</a
        >
      </li>
      {% endif %} {% if data.has_next %}
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-target="#view-container"
          hx-get="{% url 'employee-filter-view' %}?{{pd}}&page={{ data.next_page_number }}"
          class="oh-pagination__link"
          >{% trans "Next" %}</a
        >
      </li>
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-target="#view-container"
          hx-get="{% url 'employee-filter-view' %}?{{pd}}&page={{ data.paginator.num_pages }}"
          class="oh-pagination__link"
          >{% trans "Last" %}</a
        >
      </li>
      {% endif %}
    </ul>
  </nav>
</div>
{% else %}
<!-- start of empty page -->
<div class="oh-404">
  <img
    style="width: 150px; height: 150px"
    src="{% static 'images/ui/no-results.png' %}"
    class="oh-404__image mb-4"
  />
  <h5 class="oh-404__subtitle">
    {% trans "No search result found!" %}
  </h5>
</div>
<!-- end of empty page -->

{% endif %}


<div class="oh-modal" id="sendMailModal" role="dialog" aria-labelledby="sendMailModal" aria-hidden="true">
  <div class="oh-modal__dialog">
    <div class="oh-modal__dialog-header">
      <span class="oh-modal__dialog-title" id="sendMailModalLabel"><h5>{% trans 'Send Mail' %}</h5></span>
      <button class="oh-modal__close" aria-label="Close"><ion-icon name="close-outline"></ion-icon></button>
    </div>
    <div class="oh-modal__dialog-body" id="mail-content"></div>
  </div>
</div>

<div class="oh-modal" id="dataExport" role="dialog" aria-labelledby="dataExport" aria-hidden="true">
  <div class="oh-modal__dialog">
    <div class="oh-modal__dialog-header">
      <span class="oh-modal__dialog-title" id="dataExport"><h5>{% trans 'Export Data' %}</h5></span>
      <button class="oh-modal__close" aria-label="Close"><ion-icon name="close-outline"></ion-icon></button>
    </div>
    <div class="oh-modal__dialog-body" id="export-content"></div>
  </div>
</div>


<script src="{% static 'employee/actions.js' %}"></script>
<script>
  $(document).ready(function () {
    tickCheckboxes();
    $("#selectAllEmployees").click(function () {
      selectAllEmployees();
    });

    $("#unselectAllEmployees").click(function () {
      unselectAllEmployees();
    });
  });
	toggleColumns("employee-table","fieldContainerTable")
  if (!localStorage.getItem("employee_table")) {
		$("#fieldContainerTable").find("[type=checkbox]").prop("checked",true).change()
	}

</script>
