.oh-alert-container {
  top: 75px;
  right: 2rem;
  position: fixed;
  z-index: 9999;
  max-width: 500px;
  width: 95%;
  pointer-events: none;
  @media (max-width: 575.98px) {
    left: auto;
    right: auto;
    width: 95%;
  }
}
.oh-alert {
  opacity: 0;
  width: 100%;
  padding: 1rem;
  background-color: $white;
  box-shadow: rgba(0, 0, 0, 0.04) 0px 3px 5px;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  &:last-child {
    margin-bottom: 0;
  }
  &::before {
    content: "";
    position: relative;
    display: inline-block;
    width: 24px;
    height: 24px;
    background-size: contain;
    background-repeat: no-repeat;
    margin-right: 0.75rem;
  }
}
.oh-alert--animated {
  @include slide-right-in-pop(3.5s, 0s);
}
.oh-alert--danger {
  background-color: $alert-color-bg-danger;
  border: 1px solid $alert-color-border-danger;
  border-left: 5px solid $alert-color-hlt-danger;

  &::before {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iNDYiIGZpbGw9IiNkZDM2MzYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMTIgMi4yNWMtNS4zNzYgMC05Ljc1IDQuMzc0LTkuNzUgOS43NXM0LjM3NCA5Ljc1IDkuNzUgOS43NSA5Ljc1LTQuMzc0IDkuNzUtOS43NVMxNy4zNzYgMi4yNSAxMiAyLjI1Wm0uOTM4IDE0Ljk5NmgtMS44NzZWMTUuMzdoMS44NzZ2MS44NzVabS0uMTg4LTIuOTk2aC0xLjVsLS4yODEtNy41aDIuMDYybC0uMjgxIDcuNVoiPjwvcGF0aD4KPC9zdmc+");
  }
}
.oh-alert--warning {
  background-color: $alert-color-bg-warning;
  border: 1px solid $alert-color-border-warning;
  border-left: 5px solid $alert-color-hlt-warning;

  &::before {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iNDYiIGZpbGw9IiNmNGM3NDkiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMTIgMi4yNWMtNS4zNzYgMC05Ljc1IDQuMzc0LTkuNzUgOS43NXM0LjM3NCA5Ljc1IDkuNzUgOS43NSA5Ljc1LTQuMzc0IDkuNzUtOS43NVMxNy4zNzYgMi4yNSAxMiAyLjI1Wm0uOTM4IDE0Ljk5NmgtMS44NzZWMTUuMzdoMS44NzZ2MS44NzVabS0uMTg4LTIuOTk2aC0xLjVsLS4yODEtNy41aDIuMDYybC0uMjgxIDcuNVoiPjwvcGF0aD4KPC9zdmc+");
  }
}
.oh-alert--info {
  background-color: $alert-color-bg-info;
  border: 1px solid $alert-color-border-info;
  border-left: 5px solid $alert-color-hlt-info;
  color: darken($alert-color-hlt-info, 15%);

  &::before {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iNDYiIGZpbGw9IiMwNDQ0Y2UiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMTIgMi4yNWMtNS4zNzYgMC05Ljc1IDQuMzc0LTkuNzUgOS43NXM0LjM3NCA5Ljc1IDkuNzUgOS7NSA5Ljc1LTQuMzc0IDkuNzUtOS43NVMxNy4zNzYgMi4yNSAxMiAyLjI1Wm0uOTM4IDE0Ljk5NmgtMS44NzZWMTUuMzdoMS44NzZ2MS44NzVabS0uMTg4LTIuOTk2aC0xLjVsLS4yODEtNy41aDIuMDYybC0uMjgxIDcuNVoiPjwvcGF0aD4KPC9zdmc+");
  }
}
.oh-alert--success {
  background-color: $alert-color-bg-success;
  border: 1px solid $alert-color-border-success;
  border-left: 5px solid $alert-color-hlt-success;

  &::before {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iNDYiIGZpbGw9IiM0OWEyNDgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMTIgMi4yNWMtNS4zNzYgMC05Ljc1IDQuMzc0LTkuNzUgOS43NXM0LjM3NCA5Ljc1IDkuNzUgOS43NSA5Ljc1LTQuMzc0IDkuNzUtOS43NVMxNy4zNzYgMi4yNSAxMiAyLjI1Wm0uOTM4IDE0Ljk5NmgtMS44NzZWMTUuMzdoMS44NzZ2MS44NzVabS0uMTg4LTIuOTk2aC0xLjVsLS4yODEtNy41aDIuMDYybC0uMjgxIDcuNVoiPjwvcGF0aD4KPC9zdmc+");
  }
}

// Employee Data Display Table Alert - Compact styling
.oh-alert--info.mb-2 {
  padding: 0.5rem 0.75rem;

  .d-flex {

    span {
      font-size: 0.875rem;
      font-weight: 500;
      color: darken($alert-color-hlt-info, 20%);

      .oh-icon {
        font-size: 1rem;
        margin-right: 0.5rem;
        color: $alert-color-hlt-info;
      }
    }

    .oh-btn--secondary.oh-btn--sm {
      padding: 0.375rem 0.75rem;
      font-size: 0.8rem;
      font-weight: 500;
      border-radius: 4px;
      background-color: lighten($alert-color-hlt-info, 35%);
      color: darken($alert-color-hlt-info, 15%);
      border: 1px solid lighten($alert-color-hlt-info, 25%);
      text-decoration: none;
      transition: all 0.2s ease;

      &:hover {
        background-color: lighten($alert-color-hlt-info, 30%);
        border-color: lighten($alert-color-hlt-info, 20%);
        color: darken($alert-color-hlt-info, 20%);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  &::before {
    display: none; // Remove the default icon since we're using oh-icon
  }
}
