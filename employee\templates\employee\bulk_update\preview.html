{% extends 'index.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="oh-wrapper">
    <div class="oh-main-wrapper">
        <div class="oh-main">
            <div class="oh-main__header">
                <div class="oh-main__header-title">
                    <h1 class="oh-main__header-title-text">
                        <i class="oh-icon oh-icon--preview"></i>
                        {% trans "Bulk Update Preview" %}
                    </h1>
                    <p class="oh-main__header-subtitle">
                        {% trans "Review the changes before applying them" %}
                    </p>
                </div>
                <div class="oh-main__header-actions">
                    <a href="{% url 'bulk-update-upload' %}" class="oh-btn oh-btn--secondary">
                        <i class="oh-icon oh-icon--arrow-left"></i>
                        {% trans "Back to Upload" %}
                    </a>
                </div>
            </div>

            <div class="oh-main__body">
                <!-- Summary Card -->
                <div class="oh-card oh-card--summary mb-4">
                    <div class="oh-card__header">
                        <h3 class="oh-card__title">
                            <i class="oh-icon oh-icon--summary"></i>
                            {% trans "Update Summary" %}
                        </h3>
                    </div>
                    <div class="oh-card__body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="oh-stat">
                                    <div class="oh-stat__value">{{ total_records }}</div>
                                    <div class="oh-stat__label">{% trans "Total Records" %}</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="oh-stat oh-stat--success">
                                    <div class="oh-stat__value">{{ preview.total_employees_to_update }}</div>
                                    <div class="oh-stat__label">{% trans "Employees Found" %}</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="oh-stat oh-stat--warning">
                                    <div class="oh-stat__value">{{ preview.employees_not_found }}</div>
                                    <div class="oh-stat__label">{% trans "Not Found" %}</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="oh-stat oh-stat--danger">
                                    <div class="oh-stat__value">{{ preview.duplicate_entries }}</div>
                                    <div class="oh-stat__label">{% trans "Duplicates" %}</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="oh-operation-info mt-3">
                            <p><strong>{% trans "Operation Type:" %}</strong> {{ operation.get_operation_type_display }}</p>
                            <p><strong>{% trans "File:" %}</strong> {{ operation.original_filename }}</p>
                            <p><strong>{% trans "File Size:" %}</strong> {{ operation.file_size|filesizeformat }}</p>
                        </div>
                    </div>
                </div>

                <!-- Validation Errors -->
                {% if preview.validation_errors %}
                <div class="oh-card oh-card--danger mb-4">
                    <div class="oh-card__header">
                        <h3 class="oh-card__title">
                            <i class="oh-icon oh-icon--error"></i>
                            {% trans "Validation Errors" %}
                        </h3>
                    </div>
                    <div class="oh-card__body">
                        <div class="oh-alert oh-alert--danger">
                            {% trans "The following errors were found in your data. Please fix these issues and re-upload your file." %}
                        </div>
                        <div class="oh-table-wrapper">
                            <table class="oh-table">
                                <thead>
                                    <tr>
                                        <th>{% trans "Row" %}</th>
                                        <th>{% trans "Error" %}</th>
                                        <th>{% trans "Data" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for error in preview.validation_errors %}
                                    <tr>
                                        <td>{{ error.row }}</td>
                                        <td>{{ error.error }}</td>
                                        <td>
                                            <details>
                                                <summary>{% trans "View Data" %}</summary>
                                                <pre>{{ error.data|pprint }}</pre>
                                            </details>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Preview Data -->
                <div class="oh-card">
                    <div class="oh-card__header">
                        <h3 class="oh-card__title">
                            <i class="oh-icon oh-icon--preview"></i>
                            {% trans "Preview Changes" %}
                            {% if showing_records < total_records %}
                                <span class="oh-badge oh-badge--info">
                                    {% blocktrans %}Showing {{ showing_records }} of {{ total_records }} records{% endblocktrans %}
                                </span>
                            {% endif %}
                        </h3>
                    </div>
                    <div class="oh-card__body">
                        <div class="oh-table-wrapper">
                            <table class="oh-table oh-table--striped">
                                <thead>
                                    <tr>
                                        <th>{% trans "Row" %}</th>
                                        <th>{% trans "Employee" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Updates" %}</th>
                                        <th>{% trans "Issues" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for row in preview_data %}
                                    <tr class="{% if not row.employee_found %}oh-table__row--warning{% endif %}">
                                        <td>{{ row.row_number }}</td>
                                        <td>
                                            {% if row.employee_found %}
                                                <div class="oh-employee-info">
                                                    <strong>{{ row.employee_name }}</strong><br>
                                                    <small>{{ row.employee_identifier_value }}</small>
                                                </div>
                                            {% else %}
                                                <div class="oh-employee-info oh-employee-info--not-found">
                                                    <strong>{% trans "Not Found" %}</strong><br>
                                                    <small>{{ row.employee_identifier_value }}</small>
                                                </div>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if row.employee_found %}
                                                <span class="oh-badge oh-badge--success">{% trans "Found" %}</span>
                                            {% else %}
                                                <span class="oh-badge oh-badge--warning">{% trans "Not Found" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if row.updates %}
                                                <details>
                                                    <summary>
                                                        {% trans "View Updates" %}
                                                        <span class="oh-badge oh-badge--info">
                                                            {{ row.updates|length }} {% trans "fields" %}
                                                        </span>
                                                    </summary>
                                                    <div class="oh-updates-preview">
                                                        {% for model, fields in row.updates.items %}
                                                            {% if fields %}
                                                                <h5>{{ model|title }}</h5>
                                                                <ul>
                                                                    {% for field, value in fields.items %}
                                                                        <li><strong>{{ field }}:</strong> {{ value }}</li>
                                                                    {% endfor %}
                                                                </ul>
                                                            {% endif %}
                                                        {% endfor %}
                                                    </div>
                                                </details>
                                            {% else %}
                                                <span class="oh-text-muted">{% trans "No updates" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if row.errors %}
                                                <ul class="oh-error-list">
                                                    {% for error in row.errors %}
                                                        <li class="oh-text-danger">{{ error }}</li>
                                                    {% endfor %}
                                                </ul>
                                            {% endif %}
                                            {% if row.warnings %}
                                                <ul class="oh-warning-list">
                                                    {% for warning in row.warnings %}
                                                        <li class="oh-text-warning">{{ warning }}</li>
                                                    {% endfor %}
                                                </ul>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="5" class="oh-table__empty">
                                            {% trans "No data to preview" %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Confirmation Form -->
                {% if preview.total_employees_to_update > 0 and not preview.validation_errors %}
                <div class="oh-card oh-card--confirmation mt-4">
                    <div class="oh-card__header">
                        <h3 class="oh-card__title">
                            <i class="oh-icon oh-icon--confirm"></i>
                            {% trans "Confirm Updates" %}
                        </h3>
                    </div>
                    <div class="oh-card__body">
                        <div class="oh-alert oh-alert--warning mb-3">
                            <i class="oh-icon oh-icon--warning"></i>
                            {% trans "This action will update" %} <strong>{{ preview.total_employees_to_update }}</strong> {% trans "employee records and cannot be undone." %}
                        </div>
                        
                        <form method="post" class="oh-form">
                            {% csrf_token %}
                            {{ form.operation_id }}
                            
                            <div class="oh-form__group">
                                <div class="oh-checkbox-wrapper">
                                    {{ form.confirm_update }}
                                    <label for="{{ form.confirm_update.id_for_label }}" class="oh-form__label">
                                        {{ form.confirm_update.label }}
                                    </label>
                                </div>
                                {% if form.confirm_update.help_text %}
                                    <small class="oh-form__help-text">{{ form.confirm_update.help_text }}</small>
                                {% endif %}
                                {% if form.confirm_update.errors %}
                                    <div class="oh-form__error">{{ form.confirm_update.errors }}</div>
                                {% endif %}
                            </div>

                            <div class="oh-form__actions">
                                <button type="submit" class="oh-btn oh-btn--primary oh-btn--large">
                                    <i class="oh-icon oh-icon--check"></i>
                                    {% trans "Execute Bulk Update" %}
                                </button>
                                <a href="{% url 'bulk-update-upload' %}" class="oh-btn oh-btn--secondary">
                                    {% trans "Cancel" %}
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.oh-stat {
    text-align: center;
    padding: 1rem;
    border-radius: 8px;
    background: #f8f9fa;
}

.oh-stat--success { background: #d4edda; }
.oh-stat--warning { background: #fff3cd; }
.oh-stat--danger { background: #f8d7da; }

.oh-stat__value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.oh-stat__label {
    font-size: 0.875rem;
    color: #6c757d;
}

.oh-updates-preview {
    max-height: 200px;
    overflow-y: auto;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    margin-top: 0.5rem;
}

.oh-employee-info--not-found {
    color: #dc3545;
}

.oh-error-list, .oh-warning-list {
    margin: 0;
    padding-left: 1rem;
    font-size: 0.875rem;
}

.oh-error-list li {
    color: #dc3545;
}

.oh-warning-list li {
    color: #ffc107;
}
</style>
{% endblock %}
